# routers/validate_api.py
from fastapi import APIRouter, status 
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import os
from utils.gdrive_utils import download_gdrive_folder_to_data_dir
import traceback
import logging
router = APIRouter()

BASE_DIR = "data_company_documents"
SUPPORTED_EXTENSIONS = {".pdf", ".xlsx"}
os.makedirs(BASE_DIR, exist_ok=True)

class ValidateRequest(BaseModel):
    dir_url: str

@router.post("/api/ai/validate-documents")
async def validate_documents_by_dir(req: ValidateRequest):
    try:
        data_dir = download_gdrive_folder_to_data_dir(
            req.dir_url,
            base_dir=BASE_DIR,
            service_account_file="service_accounts/gdrive_readonly_key.json"
        )
        company_id = os.path.basename(data_dir)
        company_folder = os.path.join(BASE_DIR, company_id)
    except Exception as e:
        tb = traceback.format_exc()
        logging.error(f"[ERROR] Google Drive validation failed: {str(e)}")
        logging.error(f"[TRACEBACK]\n{tb}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"status": "error", "reason": "Invalid or inaccessible Google Drive folder", "trace": tb}
        )

    if not os.path.exists(company_folder):
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"status": "error", "reason": f"Directory not found: {company_folder}"}
        )

    files = os.listdir(company_folder)
    valid_count, invalid_count = 0, 0
    details = []

    for filename in files:
        ext = os.path.splitext(filename)[-1].lower()
        if ext in SUPPORTED_EXTENSIONS:
            valid_count += 1
            details.append({"document": filename, "status": "valid", "reason": f"File extension {ext} is supported"})
        else:
            invalid_count += 1
            details.append({"document": filename, "status": "invalid", "reason": f"Unsupported file extension: {ext}"})

    overall_status = "success" if valid_count > 0 else "error"
    status_code = status.HTTP_200_OK if valid_count > 0 else status.HTTP_400_BAD_REQUEST

    return JSONResponse(
        status_code=status_code,
        content={
            "status": overall_status,
            "summary": {"valid_count": valid_count, "invalid_count": invalid_count},
            "details": details
        }
    )
