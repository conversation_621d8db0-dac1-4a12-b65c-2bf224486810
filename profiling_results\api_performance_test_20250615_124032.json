[{"operation": "POST__api_ai_validate-documents", "duration_seconds": 2.072, "memory_start_mb": 37.88, "memory_end_mb": 38.3, "memory_delta_mb": 0.42, "cpu_percent": 0.0, "timestamp": "2025-06-15T12:40:26.644902", "endpoint": "/api/ai/validate-documents", "method": "POST", "description": "Document Validation", "status_code": 500, "response_time_ms": 2062.75, "response_size_bytes": 21, "success": false}, {"operation": "POST__api_ai_analyse", "duration_seconds": 2.044, "memory_start_mb": 38.31, "memory_end_mb": 38.32, "memory_delta_mb": 0.01, "cpu_percent": 0.0, "timestamp": "2025-06-15T12:40:28.689537", "endpoint": "/api/ai/analyse", "method": "POST", "description": "Document Analysis & Embedding", "status_code": 500, "response_time_ms": 2042.64, "response_size_bytes": 21, "success": false}, {"operation": "GET__api_ai_get-results_test_company", "duration_seconds": 2.047, "memory_start_mb": 38.32, "memory_end_mb": 38.32, "memory_delta_mb": 0.0, "cpu_percent": 0.8, "timestamp": "2025-06-15T12:40:30.737615", "endpoint": "/api/ai/get-results/test_company", "method": "GET", "description": "Financial Analysis Results", "status_code": 500, "response_time_ms": 2047.05, "response_size_bytes": 21, "success": false}, {"operation": "POST__api_ai_mvp_analyse", "duration_seconds": 2.053, "memory_start_mb": 38.32, "memory_end_mb": 38.33, "memory_delta_mb": 0.01, "cpu_percent": 0.0, "timestamp": "2025-06-15T12:40:32.791731", "endpoint": "/api/ai/mvp_analyse", "method": "POST", "description": "MVP Full Pipeline Analysis", "status_code": 500, "response_time_ms": 2053.12, "response_size_bytes": 21, "success": false}]