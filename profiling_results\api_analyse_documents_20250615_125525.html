<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
            </head>
            <body>
                <div id="app"></div>

                <script>var pyinstrumentHTMLRenderer=function(){"use strict";var is=Object.defineProperty;var ns=(F,ve,Pe)=>ve in F?is(F,ve,{enumerable:!0,configurable:!0,writable:!0,value:Pe}):F[ve]=Pe;var A=(F,ve,Pe)=>ns(F,typeof ve!="symbol"?ve+"":ve,Pe);function F(){}function ve(i){return i()}function Pe(){return Object.create(null)}function oe(i){i.forEach(ve)}function pt(i){return typeof i=="function"}function re(i,e){return i!=i?e==e:i!==e||i&&typeof i=="object"||typeof i=="function"}function ki(i){return Object.keys(i).length===0}function St(i,...e){if(i==null){for(const n of e)n(void 0);return F}const t=i.subscribe(...e);return t.unsubscribe?()=>t.unsubscribe():t}function ge(i,e,t){i.$$.on_destroy.push(St(e,t))}function Ci(i,e,t){return i.set(t),e}function u(i,e){i.appendChild(e)}function S(i,e,t){i.insertBefore(e,t||null)}function L(i){i.parentNode&&i.parentNode.removeChild(i)}function h(i){return document.createElement(i)}function V(i){return document.createElementNS("http://www.w3.org/2000/svg",i)}function I(i){return document.createTextNode(i)}function b(){return I(" ")}function Mi(){return I("")}function N(i,e,t,n){return i.addEventListener(e,t,n),()=>i.removeEventListener(e,t,n)}function vt(i){return function(e){return e.preventDefault(),i.call(this,e)}}function gt(i){return function(e){return e.stopPropagation(),i.call(this,e)}}function a(i,e,t){t==null?i.removeAttribute(e):i.getAttribute(e)!==t&&i.setAttribute(e,t)}function _t(i){let e;return{p(...t){e=t,e.forEach(n=>i.push(n))},r(){e.forEach(t=>i.splice(i.indexOf(t),1))}}}function Fi(i){return Array.from(i.childNodes)}function _e(i,e){e=""+e,i.data!==e&&(i.data=e)}function ae(i,e){i.value=e??""}function j(i,e,t,n){t==null?i.style.removeProperty(e):i.style.setProperty(e,t,"")}function Ee(i,e,t){i.classList.toggle(e,!!t)}function Pi(i,e,{bubbles:t=!1,cancelable:n=!1}={}){return new CustomEvent(i,{detail:e,bubbles:t,cancelable:n})}class Ri{constructor(e=!1){A(this,"is_svg",!1);A(this,"e");A(this,"n");A(this,"t");A(this,"a");this.is_svg=e,this.e=this.n=null}c(e){this.h(e)}m(e,t,n=null){this.e||(this.is_svg?this.e=V(t.nodeName):this.e=h(t.nodeType===11?"TEMPLATE":t.nodeName),this.t=t.tagName!=="TEMPLATE"?t:t.content,this.c(e)),this.i(n)}h(e){this.e.innerHTML=e,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(e){for(let t=0;t<this.n.length;t+=1)S(this.t,this.n[t],e)}p(e){this.d(),this.h(e),this.i(this.a)}d(){this.n.forEach(L)}}let Ye;function Xe(i){Ye=i}function wt(){if(!Ye)throw new Error("Function called outside component initialization");return Ye}function bt(i){wt().$$.on_mount.push(i)}function Ii(i){wt().$$.on_destroy.push(i)}function Li(){const i=wt();return(e,t,{cancelable:n=!1}={})=>{const s=i.$$.callbacks[e];if(s){const l=Pi(e,t,{cancelable:n});return s.slice().forEach(r=>{r.call(i,l)}),!l.defaultPrevented}return!0}}const Se=[],ke=[];let De=[];const Dt=[],Si=Promise.resolve();let yt=!1;function Di(){yt||(yt=!0,Si.then(Ht))}function Tt(i){De.push(i)}const At=new Set;let He=0;function Ht(){if(He!==0)return;const i=Ye;do{try{for(;He<Se.length;){const e=Se[He];He++,Xe(e),Hi(e.$$)}}catch(e){throw Se.length=0,He=0,e}for(Xe(null),Se.length=0,He=0;ke.length;)ke.pop()();for(let e=0;e<De.length;e+=1){const t=De[e];At.has(t)||(At.add(t),t())}De.length=0}while(Se.length);for(;Dt.length;)Dt.pop()();yt=!1,At.clear(),Xe(i)}function Hi(i){if(i.fragment!==null){i.update(),oe(i.before_update);const e=i.dirty;i.dirty=[-1],i.fragment&&i.fragment.p(i.ctx,e),i.after_update.forEach(Tt)}}function Oi(i){const e=[],t=[];De.forEach(n=>i.indexOf(n)===-1?e.push(n):t.push(n)),t.forEach(n=>n()),De=e}const nt=new Set;let Re;function Oe(){Re={r:0,c:[],p:Re}}function Ve(){Re.r||oe(Re.c),Re=Re.p}function D(i,e){i&&i.i&&(nt.delete(i),i.i(e))}function x(i,e,t,n){if(i&&i.o){if(nt.has(i))return;nt.add(i),Re.c.push(()=>{nt.delete(i),n&&(t&&i.d(1),n())}),i.o(e)}else n&&n()}function Ot(i){return(i==null?void 0:i.length)!==void 0?i:Array.from(i)}function Vi(i,e){x(i,1,1,()=>{e.delete(i.key)})}function Ni(i,e,t,n,s,l,r,o,c,d,v,p){let m=i.length,f=l.length,g=m;const w={};for(;g--;)w[i[g].key]=g;const E=[],C=new Map,y=new Map,k=[];for(g=f;g--;){const M=p(s,l,g),_=t(M);let T=r.get(_);T?k.push(()=>T.p(M,e)):(T=d(_,M),T.c()),C.set(_,E[g]=T),_ in w&&y.set(_,Math.abs(g-w[_]))}const H=new Set,W=new Set;function P(M){D(M,1),M.m(o,v),r.set(M.key,M),v=M.first,f--}for(;m&&f;){const M=E[f-1],_=i[m-1],T=M.key,R=_.key;M===_?(v=M.first,m--,f--):C.has(R)?!r.has(T)||H.has(T)?P(M):W.has(R)?m--:y.get(T)>y.get(R)?(W.add(T),P(M)):(H.add(R),m--):(c(_,r),m--)}for(;m--;){const M=i[m];C.has(M.key)||c(M,r)}for(;f;)P(E[f-1]);return oe(k),E}function we(i){i&&i.c()}function ce(i,e,t){const{fragment:n,after_update:s}=i.$$;n&&n.m(e,t),Tt(()=>{const l=i.$$.on_mount.map(ve).filter(pt);i.$$.on_destroy?i.$$.on_destroy.push(...l):oe(l),i.$$.on_mount=[]}),s.forEach(Tt)}function ue(i,e){const t=i.$$;t.fragment!==null&&(Oi(t.after_update),oe(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function xi(i,e){i.$$.dirty[0]===-1&&(Se.push(i),Di(),i.$$.dirty.fill(0)),i.$$.dirty[e/31|0]|=1<<e%31}function de(i,e,t,n,s,l,r=null,o=[-1]){const c=Ye;Xe(i);const d=i.$$={fragment:null,ctx:[],props:l,update:F,not_equal:s,bound:Pe(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(c?c.$$.context:[])),callbacks:Pe(),dirty:o,skip_bound:!1,root:e.target||c.$$.root};r&&r(d.root);let v=!1;if(d.ctx=t?t(i,e.props||{},(p,m,...f)=>{const g=f.length?f[0]:m;return d.ctx&&s(d.ctx[p],d.ctx[p]=g)&&(!d.skip_bound&&d.bound[p]&&d.bound[p](g),v&&xi(i,p)),m}):[],d.update(),v=!0,oe(d.before_update),d.fragment=n?n(d.ctx):!1,e.target){if(e.hydrate){const p=Fi(e.target);d.fragment&&d.fragment.l(p),p.forEach(L)}else d.fragment&&d.fragment.c();e.intro&&D(i.$$.fragment),ce(i,e.target,e.anchor),Ht()}Xe(c)}class fe{constructor(){A(this,"$$");A(this,"$$set")}$destroy(){ue(this,1),this.$destroy=F}$on(e,t){if(!pt(t))return F;const n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(t),()=>{const s=n.indexOf(t);s!==-1&&n.splice(s,1)}}$set(e){this.$$set&&!ki(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const $i="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add($i);function Bi(i){let e,t;return{c(){e=V("svg"),t=V("path"),a(t,"fill-rule","evenodd"),a(t,"clip-rule","evenodd"),a(t,"d","M5.11634 0.889422C4.86506 -0.296474 3.17237 -0.296474 2.92109 0.889422C2.78291 1.54158 2.10994 1.93011 1.47607 1.72371C0.323418 1.34837 -0.522932 2.81429 0.378448 3.62484C0.87414 4.07059 0.87414 4.84767 0.378448 5.29341C-0.522931 6.10397 0.323418 7.56989 1.47607 7.19455C2.10994 6.98814 2.78291 7.37668 2.92109 8.02883C3.17237 9.21473 4.86506 9.21473 5.11634 8.02883C5.25452 7.37668 5.92749 6.98814 6.56136 7.19455C7.71401 7.56989 8.56036 6.10397 7.65898 5.29341C7.16329 4.84767 7.16329 4.07059 7.65898 3.62484C8.56036 2.81429 7.71401 1.34837 6.56136 1.72371C5.92749 1.93011 5.25452 1.54158 5.11634 0.889422ZM4.01883 6.33408C5.05436 6.33408 5.89383 5.49462 5.89383 4.45908C5.89383 3.42355 5.05436 2.58408 4.01883 2.58408C2.98329 2.58408 2.14383 3.42355 2.14383 4.45908C2.14383 5.49462 2.98329 6.33408 4.01883 6.33408Z"),a(t,"fill","currentColor"),a(e,"width","9"),a(e,"height","9"),a(e,"viewBox","0 0 9 9"),a(e,"fill","none"),a(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){S(n,e,s),u(e,t)},p:F,i:F,o:F,d(n){n&&L(e)}}}class zi extends fe{constructor(e){super(),de(this,e,null,Bi,re,{})}}function Wi(i){let e,t,n,s,l,r,o,c,d,v,p,m,f,g,w,E,C;return{c(){e=V("svg"),t=V("g"),n=V("path"),s=V("path"),l=V("defs"),r=V("filter"),o=V("feFlood"),c=V("feBlend"),d=V("feGaussianBlur"),v=V("linearGradient"),p=V("stop"),m=V("stop"),f=V("stop"),g=V("linearGradient"),w=V("stop"),E=V("stop"),C=V("stop"),a(n,"fill-rule","evenodd"),a(n,"clip-rule","evenodd"),a(n,"d","M30 9H10V11.5H30V9ZM30 19H12.5V21.5H30V19ZM12.5 14H32.5V16.5H12.5V14ZM20 24H12.5V26.5H20V24ZM12.5 29H20V31.5H12.5V29ZM22.5 34H10V36.5H22.5V34Z"),a(n,"fill","url(#paint0_linear_67_262)"),a(t,"opacity","0.5"),a(t,"filter","url(#filter0_f_67_262)"),a(s,"fill-rule","evenodd"),a(s,"clip-rule","evenodd"),a(s,"d","M30 9H10V11.5H30V9ZM30 19H12.5V21.5H30V19ZM12.5 14H32.5V16.5H12.5V14ZM20 24H12.5V26.5H20V24ZM12.5 29H20V31.5H12.5V29ZM22.5 34H10V36.5H22.5V34Z"),a(s,"fill","url(#paint1_linear_67_262)"),a(o,"flood-opacity","0"),a(o,"result","BackgroundImageFix"),a(c,"mode","normal"),a(c,"in","SourceGraphic"),a(c,"in2","BackgroundImageFix"),a(c,"result","shape"),a(d,"stdDeviation","3.39785"),a(d,"result","effect1_foregroundBlur_67_262"),a(r,"id","filter0_f_67_262"),a(r,"x","3.2043"),a(r,"y","2.2043"),a(r,"width","36.0914"),a(r,"height","41.0914"),a(r,"filterUnits","userSpaceOnUse"),a(r,"color-interpolation-filters","sRGB"),a(p,"stop-color","#FFAA00"),a(m,"offset","0.514478"),a(m,"stop-color","#FFEB00"),a(f,"offset","1"),a(f,"stop-color","#98FF05"),a(v,"id","paint0_linear_67_262"),a(v,"x1","7.3769"),a(v,"y1","18.4566"),a(v,"x2","20.6583"),a(v,"y2","33.1038"),a(v,"gradientUnits","userSpaceOnUse"),a(w,"stop-color","#FFC834"),a(E,"offset","0.514478"),a(E,"stop-color","#FAF534"),a(C,"offset","1"),a(C,"stop-color","#B8FF38"),a(g,"id","paint1_linear_67_262"),a(g,"x1","7.3769"),a(g,"y1","18.4566"),a(g,"x2","20.6583"),a(g,"y2","33.1038"),a(g,"gradientUnits","userSpaceOnUse"),a(e,"width","44"),a(e,"height","44"),a(e,"viewBox","0 0 44 44"),a(e,"fill","none"),a(e,"xmlns","http://www.w3.org/2000/svg")},m(y,k){S(y,e,k),u(e,t),u(t,n),u(e,s),u(e,l),u(l,r),u(r,o),u(r,c),u(r,d),u(l,v),u(v,p),u(v,m),u(v,f),u(l,g),u(g,w),u(g,E),u(g,C)},p:F,i:F,o:F,d(y){y&&L(e)}}}class qi extends fe{constructor(e){super(),de(this,e,null,Wi,re,{})}}const Ne=[];function Ui(i,e){return{subscribe:st(i,e).subscribe}}function st(i,e=F){let t;const n=new Set;function s(o){if(re(i,o)&&(i=o,t)){const c=!Ne.length;for(const d of n)d[1](),Ne.push(d,i);if(c){for(let d=0;d<Ne.length;d+=2)Ne[d][0](Ne[d+1]);Ne.length=0}}}function l(o){s(o(i))}function r(o,c=F){const d=[o,c];return n.add(d),n.size===1&&(t=e(s,l)||F),o(i),()=>{n.delete(d),n.size===0&&t&&(t(),t=null)}}return{set:s,update:l,subscribe:r}}function Vt(i,e,t){const n=!Array.isArray(i),s=n?[i]:i;if(!s.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const l=e.length<2;return Ui(t,(r,o)=>{let c=!1;const d=[];let v=0,p=F;const m=()=>{if(v)return;p();const g=e(n?d[0]:d,r,o);l?r(g):p=pt(g)?g:F},f=s.map((g,w)=>St(g,E=>{d[w]=E,v&=~(1<<w),c&&m()},()=>{v|=1<<w}));return c=!0,m(),function(){oe(f),p(),c=!1}})}var Et={local:{},session:{}};function Yi(i){return i==="local"?localStorage:sessionStorage}function kt(i,e,t){var n,s,l,r,o,c,d,v;t!=null&&t.onError&&console.warn("onError has been deprecated. Please use onWriteError instead");const p=(n=t==null?void 0:t.serializer)!=null?n:JSON,m=(s=t==null?void 0:t.storage)!=null?s:"local",f=(l=t==null?void 0:t.syncTabs)!=null?l:!0,g=(o=(r=t==null?void 0:t.onWriteError)!=null?r:t==null?void 0:t.onError)!=null?o:P=>console.error(`Error when writing value from persisted store "${i}" to ${m}`,P),w=(c=t==null?void 0:t.onParseError)!=null?c:(P,M)=>console.error(`Error when parsing ${P?'"'+P+'"':"value"} from persisted store "${i}"`,M),E=(d=t==null?void 0:t.beforeRead)!=null?d:P=>P,C=(v=t==null?void 0:t.beforeWrite)!=null?v:P=>P,y=typeof window<"u"&&typeof document<"u",k=y?Yi(m):null;function H(P,M){const _=C(M);try{k==null||k.setItem(P,p.stringify(_))}catch(T){g(T)}}function W(){function P(R){try{return p.parse(R)}catch(B){w(R,B)}}const M=k==null?void 0:k.getItem(i);if(M==null)return e;const _=P(M);return _==null?e:E(_)}if(!Et[m][i]){const P=W(),M=st(P,R=>{if(y&&m=="local"&&f){const B=J=>{if(J.key===i&&J.newValue){let $;try{$=p.parse(J.newValue)}catch(ee){w(J.newValue,ee);return}const Le=E($);R(Le)}};return window.addEventListener("storage",B),()=>window.removeEventListener("storage",B)}}),{subscribe:_,set:T}=M;Et[m][i]={set(R){T(R),H(i,R)},update(R){return M.update(B=>{const J=R(B);return H(i,J),J})},reset(){this.set(e)},subscribe:_}}return Et[m][i]}function Nt(){return{collapseMode:"non-application",collapseCustomHide:"",collapseCustomShow:"",removeImportlib:!0,removeTracebackHide:!0,removePyinstrument:!0,removeIrrelevant:!0,removeIrrelevantThreshold:.001,timeFormat:"absolute"}}const Z=kt("pyinstrument:viewOptionsCallStack",Nt(),{syncTabs:!0,beforeRead(i){return{...Nt(),...i}}}),Ge=kt("pyinstrument:viewOptions",{viewMode:"call-stack"},{syncTabs:!1}),je=kt("pyinstrument:viewOptionsTimeline",{removeImportlib:!0,removeTracebackHide:!0,removePyinstrument:!0,removeIrrelevant:!0,removeIrrelevantThreshold:1e-4},{syncTabs:!0});class Xi extends Error{constructor(e){super(`Unreachable case: ${e}`)}}function Gi(i,e){const t=e*(i.length-1),n=Math.floor(t),s=Math.ceil(t),l=i[n],r=i[s],o=t-n;return Zi(o,{to:[l,r]})}function ji(i,e,t){return i===1/0?(console.warn("clamp: value is Infinity, returning `max`",i),t):i===-1/0?(console.warn("clamp: value is -Infinity, returning `min`",i),e):Number.isFinite(i)?i<e?e:i>t?t:i:(console.warn("clamp: value isn't finite, returning `min`",i),e)}function xe(i,e){const{from:t=[0,1],to:n=[0,1]}=e,s=e.clamp||!1;let l=(i-t[0])/(t[1]-t[0])*(n[1]-n[0])+n[0];return s&&(l=ji(l,Math.min(n[0],n[1]),Math.max(n[0],n[1]))),l}function Zi(i,e){return`rgb(
      ${xe(i,{from:e.from,to:[e.to[0][0],e.to[1][0]],clamp:e.clamp})},
      ${xe(i,{from:e.from,to:[e.to[0][1],e.to[1][1]],clamp:e.clamp})},
      ${xe(i,{from:e.from,to:[e.to[0][2],e.to[1][2]],clamp:e.clamp})}
    )`}function Ki(i){if(i.substr(0,1)=="#"){var e=(i.length-1)/3,t=[17,1,.062272][e-1];return[Math.round(parseInt(i.substr(1,e),16)*t),Math.round(parseInt(i.substr(1+e,e),16)*t),Math.round(parseInt(i.substr(1+2*e,e),16)*t)]}else return i.split("(")[1].split(")")[0].split(",").map(n=>+n)}function Qi(i,e,t={}){const{ignore:n=[],capture:s=!0}=t,l=window;if(!l)return()=>{};let r=!0,o=!1;const c=f=>n.some(g=>typeof g=="string"?Array.from(document.querySelectorAll(g)).some(w=>w===f.target||f.composedPath().includes(w)):g&&(f.target===g||f.composedPath().includes(g))),d=f=>{if(!(!i||i===f.target||f.composedPath().includes(i))){if(f.detail===0&&(r=!c(f)),!r){r=!0;return}e(f)}},v=f=>{o||(o=!0,setTimeout(()=>{o=!1},0),d(f))},p=f=>{r=!c(f)&&!!(i&&!f.composedPath().includes(i))};return l.addEventListener("click",v,{passive:!0,capture:s}),l.addEventListener("pointerdown",p,{passive:!0}),()=>{l.removeEventListener("click",v,{capture:s}),l.removeEventListener("pointerdown",p)}}function Ji(i){const e=document.createElement("div");return e.appendChild(document.createTextNode(i)),e.innerHTML}function Ct(i){return Ji(i).replace(/(\/|\\)/g,t=>`${t}<wbr>`)}function en(i,e){if(i.length==0)return null;let t=i[0],n=e(t);for(const s of i){const l=e(s);l>n&&(t=s,n=l)}return t}function ot(){return Math.random().toString(36).substring(2)}function tn(i){let e,t,n,s,l,r,o,c,d,v,p,m,f,g,w,E,C,y,k,H,W,P,M,_,T,R,B,J,$,Le,ee,Q,Y,Ce,q,Qe,Je,le,U,et,te,he,me,be,pe,Te,tt,Ae,K,Be,Me,it,z,O,X,fi,at,hi,mi,ze,Fe,pi,We,ct,vi,gi,ye,_i,wi,qe,ut,bi,Ue,dt,ft,ie,yi,Ti,ht,mt,ne,Ai,Rt,It,Lt,Ei;return Rt=_t(i[5][0]),It=_t(i[5][1]),{c(){e=h("div"),t=h("div"),n=h("div"),n.textContent="Collapse frames",s=b(),l=h("div"),r=h("div"),o=h("input"),c=b(),d=h("label"),v=I("Library code"),p=b(),m=h("div"),m.textContent="Code run from the Python stdlib, a virtualenv, or a conda env will be collapsed.",f=b(),g=h("div"),w=h("input"),E=b(),C=h("label"),y=I("Custom"),k=b(),H=h("div"),W=I(`Regex on the source file path.
          `),P=h("div"),M=h("label"),M.textContent="Show",_=b(),T=h("input"),R=b(),B=h("label"),B.textContent="Hide",J=b(),$=h("input"),Le=I(`
          If neither match, the library code rule is used.`),ee=b(),Q=h("div"),Y=h("input"),Ce=b(),q=h("label"),Qe=I("Disabled"),Je=b(),le=h("div"),U=h("div"),U.textContent="Remove frames",et=b(),te=h("div"),he=h("div"),me=h("input"),be=b(),pe=h("label"),Te=I("importlib machinery"),tt=b(),Ae=h("div"),K=h("input"),Be=b(),Me=h("label"),it=I("Frames declaring __traceback_hide__"),z=b(),O=h("div"),X=h("input"),fi=b(),at=h("label"),hi=I("pyinstrument frames"),mi=b(),ze=h("div"),Fe=h("input"),pi=b(),We=h("span"),ct=h("label"),vi=I("Frames with durations less than"),gi=b(),ye=h("input"),_i=I(`
          % of the total time`),wi=b(),qe=h("div"),ut=h("div"),ut.textContent="Time format",bi=b(),Ue=h("div"),dt=h("div"),ft=h("label"),ie=h("input"),yi=I(`
          Absolute time in seconds`),Ti=b(),ht=h("div"),mt=h("label"),ne=h("input"),Ai=I(`
          Percentage of the total run time`),a(n,"class","name svelte-1pecl4m"),a(o,"id",i[1]+"collapseModeAll"),a(o,"type","radio"),o.__value="non-application",ae(o,o.__value),a(o,"class","svelte-1pecl4m"),a(d,"for",i[1]+"collapseModeAll"),a(m,"class","description svelte-1pecl4m"),a(r,"class","option svelte-1pecl4m"),a(w,"id",i[1]+"collapseModeCustom"),a(w,"type","radio"),w.__value="custom",ae(w,w.__value),a(w,"class","svelte-1pecl4m"),a(C,"for",i[1]+"collapseModeCustom"),a(M,"for","collapseCustomShow"),a(M,"class","svelte-1pecl4m"),a(T,"id","collapseCustomShow"),a(T,"type","text"),a(T,"placeholder","myproject"),a(T,"spellcheck","false"),a(T,"autocapitalize","off"),a(T,"autocomplete","off"),a(T,"autocorrect","off"),a(T,"class","svelte-1pecl4m"),a(B,"for","collapseCustomHide"),a(B,"class","svelte-1pecl4m"),a($,"id","collapseCustomHide"),a($,"type","text"),a($,"placeholder",".*/lib/.*"),a($,"spellcheck","false"),a($,"autocapitalize","off"),a($,"autocomplete","off"),a($,"autocorrect","off"),a($,"class","svelte-1pecl4m"),a(P,"class","mini-input-grid svelte-1pecl4m"),a(H,"class","description svelte-1pecl4m"),a(g,"class","option svelte-1pecl4m"),a(Y,"id",i[1]+"collapseModeDisabled"),a(Y,"type","radio"),Y.__value="disabled",ae(Y,Y.__value),a(Y,"class","svelte-1pecl4m"),a(q,"for",i[1]+"collapseModeDisabled"),a(Q,"class","option svelte-1pecl4m"),a(l,"class","body"),a(t,"class","option-group svelte-1pecl4m"),a(U,"class","name svelte-1pecl4m"),a(me,"id",i[1]+"removeImportlib"),a(me,"type","checkbox"),a(me,"class","svelte-1pecl4m"),a(pe,"for",i[1]+"removeImportlib"),a(he,"class","option svelte-1pecl4m"),a(K,"id",i[1]+"removeTracebackHide"),a(K,"type","checkbox"),a(K,"class","svelte-1pecl4m"),a(Me,"for",i[1]+"removeTracebackHide"),a(Ae,"class","option svelte-1pecl4m"),a(X,"id",i[1]+"removePyinstrument"),a(X,"type","checkbox"),a(X,"class","svelte-1pecl4m"),a(at,"for",i[1]+"removePyinstrument"),a(O,"class","option svelte-1pecl4m"),a(Fe,"id",i[1]+"removeIrrelevant"),a(Fe,"type","checkbox"),a(Fe,"class","svelte-1pecl4m"),a(ct,"for",i[1]+"removeIrrelevant"),a(ye,"type","number"),ye.value=i[2](),a(ye,"min","0"),a(ye,"max","99"),a(ye,"step","0.01"),j(ye,"width","4em"),a(ye,"class","svelte-1pecl4m"),a(ze,"class","option svelte-1pecl4m"),a(te,"class","body"),a(le,"class","option-group svelte-1pecl4m"),a(ut,"class","name svelte-1pecl4m"),a(ie,"type","radio"),ie.__value="absolute",ae(ie,ie.__value),a(ie,"class","svelte-1pecl4m"),a(dt,"class","option svelte-1pecl4m"),a(ne,"type","radio"),ne.__value="proportion",ae(ne,ne.__value),a(ne,"class","svelte-1pecl4m"),a(ht,"class","option svelte-1pecl4m"),a(Ue,"class","body"),a(qe,"class","option-group svelte-1pecl4m"),a(e,"class","view-options-call-stack svelte-1pecl4m"),Rt.p(ie,ne),It.p(o,w,Y)},m(G,se){S(G,e,se),u(e,t),u(t,n),u(t,s),u(t,l),u(l,r),u(r,o),o.checked=o.__value===i[0].collapseMode,u(r,c),u(r,d),u(d,v),u(r,p),u(r,m),u(l,f),u(l,g),u(g,w),w.checked=w.__value===i[0].collapseMode,u(g,E),u(g,C),u(C,y),u(g,k),u(g,H),u(H,W),u(H,P),u(P,M),u(P,_),u(P,T),ae(T,i[0].collapseCustomShow),u(P,R),u(P,B),u(P,J),u(P,$),ae($,i[0].collapseCustomHide),u(H,Le),u(l,ee),u(l,Q),u(Q,Y),Y.checked=Y.__value===i[0].collapseMode,u(Q,Ce),u(Q,q),u(q,Qe),u(e,Je),u(e,le),u(le,U),u(le,et),u(le,te),u(te,he),u(he,me),me.checked=i[0].removeImportlib,u(he,be),u(he,pe),u(pe,Te),u(te,tt),u(te,Ae),u(Ae,K),K.checked=i[0].removeTracebackHide,u(Ae,Be),u(Ae,Me),u(Me,it),u(te,z),u(te,O),u(O,X),X.checked=i[0].removePyinstrument,u(O,fi),u(O,at),u(at,hi),u(te,mi),u(te,ze),u(ze,Fe),Fe.checked=i[0].removeIrrelevant,u(ze,pi),u(ze,We),u(We,ct),u(ct,vi),u(We,gi),u(We,ye),u(We,_i),u(e,wi),u(e,qe),u(qe,ut),u(qe,bi),u(qe,Ue),u(Ue,dt),u(dt,ft),u(ft,ie),ie.checked=ie.__value===i[0].timeFormat,u(ft,yi),u(Ue,Ti),u(Ue,ht),u(ht,mt),u(mt,ne),ne.checked=ne.__value===i[0].timeFormat,u(mt,Ai),Lt||(Ei=[N(o,"change",i[4]),N(w,"change",i[6]),N(T,"input",i[7]),N($,"input",i[8]),N(Y,"change",i[9]),N(me,"change",i[10]),N(K,"change",i[11]),N(X,"change",i[12]),N(Fe,"change",i[13]),N(ye,"input",i[3]),N(ie,"change",i[14]),N(ne,"change",i[15])],Lt=!0)},p(G,[se]){se&1&&(o.checked=o.__value===G[0].collapseMode),se&1&&(w.checked=w.__value===G[0].collapseMode),se&1&&T.value!==G[0].collapseCustomShow&&ae(T,G[0].collapseCustomShow),se&1&&$.value!==G[0].collapseCustomHide&&ae($,G[0].collapseCustomHide),se&1&&(Y.checked=Y.__value===G[0].collapseMode),se&1&&(me.checked=G[0].removeImportlib),se&1&&(K.checked=G[0].removeTracebackHide),se&1&&(X.checked=G[0].removePyinstrument),se&1&&(Fe.checked=G[0].removeIrrelevant),se&1&&(ie.checked=ie.__value===G[0].timeFormat),se&1&&(ne.checked=ne.__value===G[0].timeFormat)},i:F,o:F,d(G){G&&L(e),Rt.r(),It.r(),Lt=!1,oe(Ei)}}}function nn(i,e,t){let n;ge(i,Z,k=>t(0,n=k));const s=ot();function l(){return(n.removeIrrelevantThreshold*100).toLocaleString(void 0,{maximumFractionDigits:4})}function r(k){Ci(Z,n.removeIrrelevantThreshold=k.currentTarget.valueAsNumber/100,n)}const o=[[],[]];function c(){n.collapseMode=this.__value,Z.set(n)}function d(){n.collapseMode=this.__value,Z.set(n)}function v(){n.collapseCustomShow=this.value,Z.set(n)}function p(){n.collapseCustomHide=this.value,Z.set(n)}function m(){n.collapseMode=this.__value,Z.set(n)}function f(){n.removeImportlib=this.checked,Z.set(n)}function g(){n.removeTracebackHide=this.checked,Z.set(n)}function w(){n.removePyinstrument=this.checked,Z.set(n)}function E(){n.removeIrrelevant=this.checked,Z.set(n)}function C(){n.timeFormat=this.__value,Z.set(n)}function y(){n.timeFormat=this.__value,Z.set(n)}return[n,s,l,r,c,o,d,v,p,m,f,g,w,E,C,y]}class sn extends fe{constructor(e){super(),de(this,e,nn,tn,re,{})}}function on(i){let e,t,n,s,l,r,o,c,d,v,p,m,f,g,w,E,C,y,k,H,W,P,M,_;return{c(){e=h("div"),t=h("div"),n=h("div"),n.textContent="Remove frames",s=b(),l=h("div"),r=h("div"),o=h("input"),c=b(),d=h("label"),v=I("importlib machinery"),p=b(),m=h("div"),f=h("input"),g=b(),w=h("label"),E=I("Frames declaring __traceback_hide__"),C=b(),y=h("div"),k=h("input"),H=b(),W=h("label"),P=I("pyinstrument frames"),a(n,"class","name"),a(o,"id",i[1]+"removeImportlib"),a(o,"type","checkbox"),a(d,"for",i[1]+"removeImportlib"),a(r,"class","option"),a(f,"id",i[1]+"removeTracebackHide"),a(f,"type","checkbox"),a(w,"for",i[1]+"removeTracebackHide"),a(m,"class","option"),a(k,"id",i[1]+"removePyinstrument"),a(k,"type","checkbox"),a(W,"for",i[1]+"removePyinstrument"),a(y,"class","option"),a(l,"class","body"),a(t,"class","option-group"),a(e,"class","view-options-timeline svelte-vsz8zm")},m(T,R){S(T,e,R),u(e,t),u(t,n),u(t,s),u(t,l),u(l,r),u(r,o),o.checked=i[0].removeImportlib,u(r,c),u(r,d),u(d,v),u(l,p),u(l,m),u(m,f),f.checked=i[0].removeTracebackHide,u(m,g),u(m,w),u(w,E),u(l,C),u(l,y),u(y,k),k.checked=i[0].removePyinstrument,u(y,H),u(y,W),u(W,P),M||(_=[N(o,"change",i[2]),N(f,"change",i[3]),N(k,"change",i[4])],M=!0)},p(T,[R]){R&1&&(o.checked=T[0].removeImportlib),R&1&&(f.checked=T[0].removeTracebackHide),R&1&&(k.checked=T[0].removePyinstrument)},i:F,o:F,d(T){T&&L(e),M=!1,oe(_)}}}function rn(i,e,t){let n;ge(i,je,c=>t(0,n=c));const s=ot();function l(){n.removeImportlib=this.checked,je.set(n)}function r(){n.removeTracebackHide=this.checked,je.set(n)}function o(){n.removePyinstrument=this.checked,je.set(n)}return[n,s,l,r,o]}class ln extends fe{constructor(e){super(),de(this,e,rn,on,re,{})}}function an(i){let e,t;return e=new ln({}),{c(){we(e.$$.fragment)},m(n,s){ce(e,n,s),t=!0},i(n){t||(D(e.$$.fragment,n),t=!0)},o(n){x(e.$$.fragment,n),t=!1},d(n){ue(e,n)}}}function cn(i){let e,t;return e=new sn({}),{c(){we(e.$$.fragment)},m(n,s){ce(e,n,s),t=!0},i(n){t||(D(e.$$.fragment,n),t=!0)},o(n){x(e.$$.fragment,n),t=!1},d(n){ue(e,n)}}}function un(i){let e,t,n,s,l,r,o,c,d;const v=[cn,an],p=[];function m(f,g){return f[0].viewMode==="call-stack"?0:f[0].viewMode==="timeline"?1:-1}return~(o=m(i))&&(c=p[o]=v[o](i)),{c(){e=h("div"),t=h("div"),n=h("div"),s=I(i[3]),l=b(),r=h("div"),c&&c.c(),a(n,"class","title-row svelte-rpk7lo"),a(r,"class","body svelte-rpk7lo"),a(t,"class","box svelte-rpk7lo"),a(e,"class","view-options svelte-rpk7lo")},m(f,g){S(f,e,g),u(e,t),u(t,n),u(n,s),u(t,l),u(t,r),~o&&p[o].m(r,null),i[4](t),i[5](e),d=!0},p(f,[g]){(!d||g&8)&&_e(s,f[3]);let w=o;o=m(f),o!==w&&(c&&(Oe(),x(p[w],1,1,()=>{p[w]=null}),Ve()),~o?(c=p[o],c||(c=p[o]=v[o](f),c.c()),D(c,1),c.m(r,null)):c=null)},i(f){d||(D(c),d=!0)},o(f){x(c),d=!1},d(f){f&&L(e),~o&&p[o].d(),i[4](null),i[5](null)}}}function dn(i,e,t){let n;ge(i,Ge,m=>t(0,n=m));const s=Li();function l(){s("close")}let r,o;bt(()=>{if(o)return Qi(o,l,{ignore:[".js-view-options-button"]})});function c(){if(!r||!o)return;const m=r.getBoundingClientRect(),g=o.getBoundingClientRect().width;m.right-g-20<0?t(2,o.style.right=`${m.right-g-20}px`,o):t(2,o.style.right="0",o)}bt(()=>(c(),window.addEventListener("resize",c),()=>window.removeEventListener("resize",c)));let d="View options";function v(m){ke[m?"unshift":"push"](()=>{o=m,t(2,o)})}function p(m){ke[m?"unshift":"push"](()=>{r=m,t(1,r)})}return i.$$.update=()=>{i.$$.dirty&1&&(n.viewMode==="call-stack"?t(3,d="Call stack view options"):n.viewMode==="timeline"&&t(3,d="Timeline view options"))},[n,r,o,d,v,p]}class fn extends fe{constructor(e){super(),de(this,e,dn,un,re,{})}}function xt(i){let e,t;return e=new fn({}),e.$on("close",i[9]),{c(){we(e.$$.fragment)},m(n,s){ce(e,n,s),t=!0},p:F,i(n){t||(D(e.$$.fragment,n),t=!0)},o(n){x(e.$$.fragment,n),t=!1},d(n){ue(e,n)}}}function hn(i){let e,t,n,s,l,r,o,c,d=Ct(i[0].target_description)+"",v,p,m,f,g,w,E,C,y,k,H,W,P,M=i[0].sampleCount+"",_,T,R,B,J,$,Le,ee,Q,Y,Ce,q,Qe,Je,le,U,et,te,he,me,be,pe,Te,tt,Ae,K,Be,Me,it;l=new qi({}),Te=new zi({});let z=i[1]&&xt(i);return Be=_t(i[7][0]),{c(){e=h("div"),t=h("div"),n=h("div"),s=h("div"),we(l.$$.fragment),r=b(),o=h("div"),c=h("div"),v=b(),p=h("div"),m=h("div"),f=h("span"),f.textContent="Recorded:",g=b(),w=h("span"),w.textContent=`${i[3]}`,E=b(),C=h("br"),y=b(),k=h("div"),H=h("span"),H.textContent="Samples:",W=b(),P=h("span"),_=I(M),T=b(),R=h("div"),B=h("span"),B.textContent="CPU utilization:",J=b(),$=h("span"),$.textContent=`${(i[4]*100).toFixed(0)}%`,Le=b(),ee=h("div"),Q=h("div"),Y=I(`View:
            `),Ce=h("label"),q=h("input"),Qe=I(`
              Call stack`),Je=b(),le=h("label"),U=h("input"),et=I(`
              Timeline`),te=b(),he=h("div"),me=b(),be=h("div"),pe=h("button"),we(Te.$$.fragment),tt=I(`
              View options`),Ae=b(),z&&z.c(),a(s,"class","logo svelte-qdxst2"),a(c,"class","target-description svelte-qdxst2"),a(f,"class","metric-label svelte-qdxst2"),a(w,"class","metric-value svelte-qdxst2"),a(m,"class","metric date svelte-qdxst2"),a(C,"class","svelte-qdxst2"),a(H,"class","metric-label svelte-qdxst2"),a(P,"class","metric-value svelte-qdxst2"),a(k,"class","metric svelte-qdxst2"),a(B,"class","metric-label svelte-qdxst2"),a($,"class","metric-value svelte-qdxst2"),a(R,"class","metric svelte-qdxst2"),a(p,"class","metrics svelte-qdxst2"),a(q,"type","radio"),q.__value="call-stack",ae(q,q.__value),a(q,"class","svelte-qdxst2"),a(Ce,"class","svelte-qdxst2"),a(U,"type","radio"),U.__value="timeline",ae(U,U.__value),a(U,"class","svelte-qdxst2"),a(le,"class","svelte-qdxst2"),a(Q,"class","toggle"),a(he,"class","spacer"),j(he,"flex","1"),a(pe,"class","js-view-options-button svelte-qdxst2"),a(be,"class","button-container svelte-qdxst2"),a(ee,"class","view-options svelte-qdxst2"),a(o,"class","layout svelte-qdxst2"),a(n,"class","row svelte-qdxst2"),a(t,"class","margins"),a(e,"class","header svelte-qdxst2"),Be.p(q,U)},m(O,X){S(O,e,X),u(e,t),u(t,n),u(n,s),ce(l,s,null),u(n,r),u(n,o),u(o,c),c.innerHTML=d,u(o,v),u(o,p),u(p,m),u(m,f),u(m,g),u(m,w),u(p,E),u(p,C),u(p,y),u(p,k),u(k,H),u(k,W),u(k,P),u(P,_),u(p,T),u(p,R),u(R,B),u(R,J),u(R,$),u(o,Le),u(o,ee),u(ee,Q),u(Q,Y),u(Q,Ce),u(Ce,q),q.checked=q.__value===i[2].viewMode,u(Ce,Qe),u(Q,Je),u(Q,le),u(le,U),U.checked=U.__value===i[2].viewMode,u(le,et),u(ee,te),u(ee,he),u(ee,me),u(ee,be),u(be,pe),ce(Te,pe,null),u(pe,tt),u(be,Ae),z&&z.m(be,null),K=!0,Me||(it=[N(q,"change",i[6]),N(U,"change",i[8]),N(pe,"click",gt(vt(i[5])))],Me=!0)},p(O,[X]){(!K||X&1)&&d!==(d=Ct(O[0].target_description)+"")&&(c.innerHTML=d),(!K||X&1)&&M!==(M=O[0].sampleCount+"")&&_e(_,M),X&4&&(q.checked=q.__value===O[2].viewMode),X&4&&(U.checked=U.__value===O[2].viewMode),O[1]?z?(z.p(O,X),X&2&&D(z,1)):(z=xt(O),z.c(),D(z,1),z.m(be,null)):z&&(Oe(),x(z,1,1,()=>{z=null}),Ve())},i(O){K||(D(l.$$.fragment,O),D(Te.$$.fragment,O),D(z),K=!0)},o(O){x(l.$$.fragment,O),x(Te.$$.fragment,O),x(z),K=!1},d(O){O&&L(e),ue(l),ue(Te),z&&z.d(),Be.r(),Me=!1,oe(it)}}}function mn(i,e,t){let n;ge(i,Ge,f=>t(2,n=f));let{session:s}=e;const l=new Date(s.startTime*1e3).toLocaleString(void 0,{dateStyle:"long",timeStyle:"medium"}),r=s.cpuTime/s.duration;let o=!1;function c(f){t(1,o=!o)}const d=[[]];function v(){n.viewMode=this.__value,Ge.set(n)}function p(){n.viewMode=this.__value,Ge.set(n)}const m=()=>t(1,o=!1);return i.$$set=f=>{"session"in f&&t(0,s=f.session)},[s,o,n,l,r,c,v,d,p,m]}class pn extends fe{constructor(e){super(),de(this,e,mn,hn,re,{session:0})}}const vn="data:image/png;base64,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",$t=st({}),Bt=st({});function zt(i){return i>.6?"#FF4159":i>.3?"#F5A623":i>.15?"#D8CB2A":i>.05?"#7ED321":"#58984f"}function Wt(i,e,t){const n=i.slice();return n[21]=e[t],n}function qt(i){let e,t,n,s,l,r,o,c,d,v,p,m,f,g,w,E,C;return{c(){e=h("div"),t=h("div"),n=V("svg"),s=V("path"),l=b(),r=h("div"),o=I(i[6]),c=b(),d=h("div"),v=I(i[4]),p=b(),m=h("div"),f=I(i[5]),g=b(),w=h("div"),a(s,"d","M.937-.016L5.793 4.84.937 9.696z"),a(s,"fill",i[8]),a(s,"fill-rule","evenodd"),a(s,"fill-opacity",".582"),a(n,"width","6"),a(n,"height","10"),a(t,"class","frame-triangle svelte-7e9kco"),Ee(t,"rotate",!i[9]),j(t,"visibility",i[0].children.length>0?"visible":"hidden"),a(r,"class","time svelte-7e9kco"),j(r,"color",i[8]),j(r,"font-weight",i[11]<.15?500:600),a(d,"class","name svelte-7e9kco"),a(m,"class","code-position svelte-7e9kco"),a(e,"class","frame-description svelte-7e9kco"),a(e,"role","button"),a(e,"tabindex","0"),Ee(e,"application-code",i[0].isApplicationCode),Ee(e,"children-visible",!i[9]),j(e,"padding-left",`${i[2]*35}px`),a(w,"class","visual-guide svelte-7e9kco"),j(w,"left",`${i[2]*35+21}px`),j(w,"background-color",i[8])},m(y,k){S(y,e,k),u(e,t),u(t,n),u(n,s),u(e,l),u(e,r),u(r,o),u(e,c),u(e,d),u(d,v),u(e,p),u(e,m),u(m,f),S(y,g,k),S(y,w,k),E||(C=[N(e,"keydown",i[14]),N(e,"click",gt(vt(i[12])))],E=!0)},p(y,k){k&256&&a(s,"fill",y[8]),k&512&&Ee(t,"rotate",!y[9]),k&1&&j(t,"visibility",y[0].children.length>0?"visible":"hidden"),k&64&&_e(o,y[6]),k&256&&j(r,"color",y[8]),k&16&&_e(v,y[4]),k&32&&_e(f,y[5]),k&1&&Ee(e,"application-code",y[0].isApplicationCode),k&512&&Ee(e,"children-visible",!y[9]),k&4&&j(e,"padding-left",`${y[2]*35}px`),k&4&&j(w,"left",`${y[2]*35+21}px`),k&256&&j(w,"background-color",y[8])},d(y){y&&(L(e),L(g),L(w)),E=!1,oe(C)}}}function Ut(i){let e,t,n,s,l=i[0].group.frames.length-1+"",r,o,c,d,v,p;return{c(){e=h("div"),t=h("div"),n=h("div"),n.innerHTML='<svg width="6" height="10"><path d="M.937-.016L5.793 4.84.937 9.696z" fill="#FFF" fill-rule="evenodd" fill-opacity=".582"></path></svg>',s=b(),r=I(l),o=I(" frames hidden ("),c=I(i[7]),d=I(")"),a(n,"class","group-triangle svelte-7e9kco"),Ee(n,"rotate",i[10]),a(t,"class","group-header-button svelte-7e9kco"),a(e,"class","group-header svelte-7e9kco"),a(e,"role","button"),a(e,"tabindex","0"),j(e,"padding-left",`${i[2]*35}px`)},m(m,f){S(m,e,f),u(e,t),u(t,n),u(t,s),u(t,r),u(t,o),u(t,c),u(t,d),v||(p=[N(e,"keydown",i[15]),N(e,"click",gt(vt(i[13])))],v=!0)},p(m,f){f&1024&&Ee(n,"rotate",m[10]),f&1&&l!==(l=m[0].group.frames.length-1+"")&&_e(r,l),f&128&&_e(c,m[7]),f&4&&j(e,"padding-left",`${m[2]*35}px`)},d(m){m&&L(e),v=!1,oe(p)}}}function Yt(i){let e,t=[],n=new Map,s,l=Ot(i[0].children);const r=o=>o[21].uuid;for(let o=0;o<l.length;o+=1){let c=Wt(i,l,o),d=r(c);n.set(d,t[o]=Xt(d,c))}return{c(){e=h("div");for(let o=0;o<t.length;o+=1)t[o].c();a(e,"class","children svelte-7e9kco")},m(o,c){S(o,e,c);for(let d=0;d<t.length;d+=1)t[d]&&t[d].m(e,null);s=!0},p(o,c){c&15&&(l=Ot(o[0].children),Oe(),t=Ni(t,c,r,1,o,l,n,e,Vi,Xt,null,Wt),Ve())},i(o){if(!s){for(let c=0;c<l.length;c+=1)D(t[c]);s=!0}},o(o){for(let c=0;c<t.length;c+=1)x(t[c]);s=!1},d(o){o&&L(e);for(let c=0;c<t.length;c+=1)t[c].d()}}}function Xt(i,e){let t,n,s;return n=new Kt({props:{frame:e[21],rootFrame:e[1],indent:e[2]+(e[3]?1:0)}}),{key:i,first:null,c(){t=Mi(),we(n.$$.fragment),this.first=t},m(l,r){S(l,t,r),ce(n,l,r),s=!0},p(l,r){e=l;const o={};r&1&&(o.frame=e[21]),r&2&&(o.rootFrame=e[1]),r&12&&(o.indent=e[2]+(e[3]?1:0)),n.$set(o)},i(l){s||(D(n.$$.fragment,l),s=!0)},o(l){x(n.$$.fragment,l),s=!1},d(l){l&&L(t),ue(n,l)}}}function gn(i){let e,t,n,s,l=i[3]&&qt(i),r=i[0].group&&i[0].group.rootFrame==i[0]&&!i[9]&&Ut(i),o=!i[9]&&i[0].children.length>0&&Yt(i);return{c(){e=h("div"),l&&l.c(),t=b(),r&&r.c(),n=b(),o&&o.c(),a(e,"class","frame svelte-7e9kco")},m(c,d){S(c,e,d),l&&l.m(e,null),u(e,t),r&&r.m(e,null),u(e,n),o&&o.m(e,null),s=!0},p(c,[d]){c[3]?l?l.p(c,d):(l=qt(c),l.c(),l.m(e,t)):l&&(l.d(1),l=null),c[0].group&&c[0].group.rootFrame==c[0]&&!c[9]?r?r.p(c,d):(r=Ut(c),r.c(),r.m(e,n)):r&&(r.d(1),r=null),!c[9]&&c[0].children.length>0?o?(o.p(c,d),d&513&&D(o,1)):(o=Yt(c),o.c(),D(o,1),o.m(e,null)):o&&(Oe(),x(o,1,1,()=>{o=null}),Ve())},i(c){s||(D(o),s=!0)},o(c){x(o),s=!1},d(c){c&&L(e),l&&l.d(),r&&r.d(),o&&o.d()}}}function Gt(){const i='a:not([disabled]), button:not([disabled]), input[type=text]:not([disabled]), [tabindex]:not([disabled]):not([tabindex="-1"])',e=document.querySelector(".call-stack-view");if(!e)throw new Error("callStackElement not found");var t=Array.prototype.filter.call(e.querySelectorAll(i),function(n){return n.offsetWidth>0||n.offsetHeight>0||n===document.activeElement});return t}function jt(){const i=Gt();var e=i.indexOf(document.activeElement);if(e>-1){var t=i[e+1];t&&t.focus()}}function Zt(){const i=Gt();var e=i.indexOf(document.activeElement);if(e>-1){var t=i[e-1];t&&t.focus()}}function _n(i,e,t){let n,s,l,r,o;ge(i,Bt,_=>t(16,l=_)),ge(i,$t,_=>t(17,r=_)),ge(i,Z,_=>t(18,o=_));let{frame:c}=e,{rootFrame:d}=e,{indent:v=0}=e,p;const m=c.time/d.time;let f,g;c.isSynthetic||c.filePathShort==null?g="":c.lineNo==null||c.lineNo===0?g=c.filePathShort:g=`${c.filePathShort}:${c.lineNo}`;let w,E=null;if(c.group){const _=c.group.libraries;_.length<4?E=_.join(", "):E=`${_[0]}, ${_[1]}, ${_[2]}...`}let C;C=zt(m);function y(_){k(c,!s,_.altKey)}function k(_,T,R=!0){if(Bt.update(B=>({...B,[_.uuid]:T})),R)for(const B of _.children)k(B,T,!0),_.group&&_.group.rootFrame==_&&H(_.group.id,!T)}function H(_,T){$t.update(R=>({...R,[_]:T}))}function W(){c.group&&H(c.group.id,!n)}function P(_){let T=!0;_.key==="Enter"||_.key===" "?y(_):_.key==="ArrowLeft"&&!s?k(c,!0,_.altKey):_.key==="ArrowRight"&&s?k(c,!1,_.altKey):_.key==="ArrowUp"?Zt():_.key==="ArrowDown"?jt():T=!1,T&&(_.preventDefault(),_.stopPropagation())}function M(_){let T=!0;_.key==="Enter"||_.key===" "?W():_.key==="ArrowLeft"&&c.group?H(c.group.id,!1):_.key==="ArrowRight"&&c.group?H(c.group.id,!0):_.key==="ArrowUp"?Zt():_.key==="ArrowDown"?jt():T=!1,T&&(_.preventDefault(),_.stopPropagation())}return i.$$set=_=>{"frame"in _&&t(0,c=_.frame),"rootFrame"in _&&t(1,d=_.rootFrame),"indent"in _&&t(2,v=_.indent)},i.$$.update=()=>{var _,T;if(i.$$.dirty&131073&&(c.group?r[c.group.id??""]||((_=c.group)==null?void 0:_.rootFrame)===c||c.children.filter(R=>!R.group).length>1?t(3,p=!0):t(3,p=!1):t(3,p=!0)),i.$$.dirty&1&&(c.className?t(4,f=`${c.className}.${c.function}`):t(4,f=c.function)),i.$$.dirty&262145)if(o.timeFormat==="absolute")t(6,w=c.time.toLocaleString(void 0,{minimumFractionDigits:3,maximumFractionDigits:3}));else if(o.timeFormat==="proportion")t(6,w=`${(m*100).toLocaleString(void 0,{minimumFractionDigits:1,maximumFractionDigits:1})}%`);else throw new Error("unknown timeFormat");i.$$.dirty&131073&&t(10,n=r[((T=c.group)==null?void 0:T.id)??""]===!0),i.$$.dirty&65537&&t(9,s=l[c.uuid]===!0)},[c,d,v,p,f,g,w,E,C,s,n,m,y,W,P,M,l,r,o]}let Kt=class extends fe{constructor(e){super(),de(this,e,_n,gn,re,{frame:0,rootFrame:1,indent:2})}};function Qt(i,e,t){let n=i;for(const s of e)if(n=s(n,t),!n)return null;return n}const wn="\0",bn="[await]",Ze="[self]",yn=[bn,Ze,"[out-of-context]","[root]"],Tn="c",An="h";class Ke{constructor(e,t){A(this,"uuid",ot());A(this,"identifier");A(this,"_identifierParts");A(this,"startTime");A(this,"time",0);A(this,"absorbedTime",0);A(this,"group",null);A(this,"attributes");A(this,"_children",[]);A(this,"parent",null);A(this,"context");var l;this.identifier=e.identifier,this._identifierParts=this.identifier.split(wn),this.startTime=e.startTime??0,this.time=e.time??0,this.attributes=e.attributes??{},this.context=t;let n=this.startTime;const s=(l=e.children)==null?void 0:l.map(r=>(r.startTime===void 0&&(r={...r,startTime:n},n+=r.time??0),n=r.startTime+(r.time??0),new Ke(r,t)));s&&this.addChildren(s)}cloneDeep(){return new Ke(this,this.context)}get children(){return this._children}addChild(e,t={}){if(e.removeFromParent(),e.parent=this,t.after){const n=this._children.indexOf(t.after);if(n==-1)throw new Error("After frame not found");this._children.splice(n+1,0,e)}else this._children.push(e)}addChildren(e,t={}){e=e.slice(),t.after?(e.slice().reverse(),e.forEach(s=>this.addChild(s,t))):e.forEach(n=>this.addChild(n,t))}removeFromParent(){if(this.parent){const e=this.parent._children.indexOf(this);this.parent._children.splice(e,1),this.parent=null}}getAttributes(e){return Object.keys(this.attributes).filter(n=>n.startsWith(e)).map(n=>({data:n.slice(1),time:this.attributes[n]}))}getAttributeValue(e){const t=this.getAttributes(e);if(!t||t.length==0)return null;let n=0;for(let s=0;s<t.length;s++)t[s].time>t[n].time&&(n=s);return t[n].data}get hasTracebackHide(){return this.getAttributeValue(An)=="1"}get function(){return this._identifierParts[0]}get filePath(){return this._identifierParts[1]??null}get lineNo(){const e=this._identifierParts[2];return e?parseInt(e):null}get isSynthetic(){return yn.includes(this.identifier)}get filePathShort(){return this.isSynthetic&&this.parent?this.parent.filePathShort:this.filePath?this.context.shortenPath(this.filePath):null}get isApplicationCode(){if(this.isSynthetic)return!1;const e=this.filePath;return!e||this.context.sysPrefixes.some(n=>e.startsWith(n))?!1:e.startsWith("<")?e.startsWith("<ipython-input-")?!0:e=="<string>"||e=="<stdin>"?this.parent?this.parent.isApplicationCode:!0:!1:!0}get proportionOfParent(){return this.parent?this.time/this.parent.time:1}get className(){return this.getAttributeValue(Tn)??""}get library(){const e=this.filePathShort;return e?/^[\\/.]*[^\\/.]*/.exec(e)[0]??"":null}}class En{constructor(e){A(this,"id");A(this,"rootFrame");A(this,"_frames",[]);this.id=ot(),this.rootFrame=e}addFrame(e){e.group&&e.group.removeFrame(e),this._frames.push(e),e.group=this}removeFrame(e){if(e.group!==this)throw new Error("Frame not in group.");const t=this._frames.indexOf(e);if(t===-1)throw new Error("Frame not found in group.");this._frames.splice(t,1),e.group=null}get frames(){return this._frames}get exitFrames(){const e=[];for(const t of this.frames){let n=!1;for(const s of t.children)if(s.group!=this){n=!0;break}n&&e.push(t)}return e}get libraries(){const e=[];for(const t of this.frames){const n=t.library;n&&(e.includes(n)||e.push(n))}return e}}function rt(i,e){const{replaceWith:t}=e,n=i.parent;if(!n)throw new Error("Cannot delete the root frame");if(t=="children")n.addChildren(i.children,{after:i});else if(t=="self_time")n.addChild(new Ke({identifier:Ze,time:i.time},n.context),{after:i});else if(t=="nothing")n.absorbedTime+=i.time;else throw new Xi(t);i.removeFromParent(),Mt(i,!0)}function kn(i,e){if(i.parent!==e.parent)throw new Error("Both frames must have the same parent.");e.absorbedTime+=i.absorbedTime,e.time+=i.time,Object.entries(i.attributes).forEach(([t,n])=>{e.attributes[t]!==void 0?e.attributes[t]+=n:e.attributes[t]=n}),e.addChildren(i.children),i.removeFromParent(),Mt(i,!1)}function Mt(i,e){if(e&&i.children&&i.children.forEach(t=>{Mt(t,!0)}),i.group){const t=i.group;t.removeFrame(i),t.frames.length===1&&t.removeFrame(t.frames[0])}}function Ft(i,e){if(!i)return null;for(const t of i.children)Ft(t),t.filePath&&t.filePath.includes("<frozen importlib._bootstrap")&&rt(t,{replaceWith:"children"});return i}function Pt(i,e){if(!i)return null;for(const t of i.children)Pt(t),t.hasTracebackHide&&rt(t,{replaceWith:"children"});return i}function Jt(i,e){if(!i)return null;const t={};for(const n of i.children.slice())if(t[n.identifier]){const s=t[n.identifier];kn(n,s)}else t[n.identifier]=n;return i.children.forEach(n=>Jt(n)),i._children.sort((n,s)=>s.time-n.time),i}function ei(i,e){if(!i)return null;const t=e.hideRegex,n=e.showRegex;function s(r){const o=r.filePath||"",c=n&&new RegExp(n).test(o),d=t&&new RegExp(t).test(o);return c?!1:d?!0:!r.isApplicationCode}function l(r,o){o.addFrame(r),r.children.forEach(c=>{s(c)&&l(c,o)})}return i.children.forEach(r=>{if(!r.group&&s(r)&&r.children.some(s)){const o=new En(r);l(r,o)}ei(r,e)}),i}function ti(i,e,t=!0){if(!i)return null;let n=null;for(const s of i.children)s.identifier===Ze?n?(n.time+=s.time,s.removeFromParent()):n=s:n=null;return t&&i.children.forEach(s=>ti(s,e,!0)),i}function ii(i,e){return i?(i.children.length===1&&i.children[0].identifier===Ze&&rt(i.children[0],{replaceWith:"nothing"}),i.children.forEach(t=>ii(t)),i):null}function ni(i,e,t=null){if(!i)return null;t===null&&(t=i.time,t<=0&&(t=1e-44));const n=e.filterThreshold??.01;for(const s of i.children.slice())s.time/t<n&&rt(s,{replaceWith:"nothing"});return i.children.forEach(s=>ni(s,e,t)),i}function si(i,e){if(!i)return null;const t=o=>en(o,c=>c.time),n=o=>{var c;return((c=o.filePath)==null?void 0:c.includes("pyinstrument/__main__.py"))&&o.children.length>0},s=o=>{var c;return o.proportionOfParent>.8&&((c=o.filePath)==null?void 0:c.includes("<string>"))&&o.children.length>0},l=o=>{var c;return o.proportionOfParent>.8&&(new RegExp(".*runpy.py").test(o.filePath??"")||((c=o.filePath)==null?void 0:c.includes("<frozen runpy>")))&&o.children.length>0};let r=i;if(!n(r)||(r=t(r.children),!s(r))||(r=t(r.children),!l(r)))return i;for(;l(r);)r=t(r.children);return r.removeFromParent(),r}function oi(i,e){return i?(i.children.forEach(t=>oi(t)),i.group&&i.group.frames.length<3&&i.group.removeFrame(i),i):null}function Cn(i){let e,t,n;return t=new Kt({props:{frame:i[3],rootFrame:i[3]}}),{c(){e=h("div"),we(t.$$.fragment),a(e,"class","call-stack-margins svelte-1hebm9u")},m(s,l){S(s,e,l),ce(t,e,null),n=!0},p(s,l){const r={};l&8&&(r.frame=s[3]),l&8&&(r.rootFrame=s[3]),t.$set(r)},i(s){n||(D(t.$$.fragment,s),n=!0)},o(s){x(t.$$.fragment,s),n=!1},d(s){s&&L(e),ue(t)}}}function Mn(i){let e;return{c(){e=h("div"),e.innerHTML='<div class="error">All frames were filtered out.</div>',a(e,"class","margins")},m(t,n){S(t,e,n)},p:F,i:F,o:F,d(t){t&&L(e)}}}function Fn(i){let e,t,n,s,l,r,o;const c=[Mn,Cn],d=[];function v(p,m){return p[3]?1:0}return n=v(i),s=d[n]=c[n](i),{c(){e=h("div"),t=h("div"),s.c(),l=b(),r=h("div"),a(t,"class","scroll-inner svelte-1hebm9u"),a(r,"class","scroll-size-fixer svelte-1hebm9u"),a(e,"class","call-stack-view svelte-1hebm9u")},m(p,m){S(p,e,m),u(e,t),d[n].m(t,null),i[7](t),u(e,l),u(e,r),i[8](r),i[9](e),o=!0},p(p,[m]){let f=n;n=v(p),n===f?d[n].p(p,m):(Oe(),x(d[f],1,1,()=>{d[f]=null}),Ve(),s=d[n],s?s.p(p,m):(s=d[n]=c[n](p),s.c()),D(s,1),s.m(t,null))},i(p){o||(D(s),o=!0)},o(p){x(s),o=!1},d(p){p&&L(e),d[n].d(),i[7](null),i[8](null),i[9](null)}}}function Pn(i,e,t){let n,{session:s}=e;const l=Vt([Z],([f])=>{const g=[f.removeImportlib?Ft:null,f.removeTracebackHide?Pt:null,ti,Jt,ii,f.removeIrrelevant?ni:null,f.removePyinstrument?si:null,f.collapseMode!=="disabled"?ei:null,oi].filter(E=>E!==null),w={filterThreshold:f.removeIrrelevantThreshold,hideRegex:f.collapseMode=="custom"?f.collapseCustomHide:void 0,showRegex:f.collapseMode=="custom"?f.collapseCustomShow:void 0};return{processors:g,options:w}});ge(i,l,f=>t(6,n=f));let r,o,c;bt(()=>{let f=0;const g=r;if(!g)throw new Error("element not set");if(!o)throw new Error("scrollInnerElement not set");if(!c)throw new Error("scrollSizeFixerElement not set");const w=new ResizeObserver(()=>{const C=o.getBoundingClientRect().height;C>f&&(f=C,t(2,c.style.top=`${f-1}px`,c))});w.observe(o);let E;return g.addEventListener("scroll",E=()=>{let C=g.scrollTop+g.clientHeight;const y=o.getBoundingClientRect().height;C<y&&(C=y),C<f&&(f=C,t(2,c.style.top=`${f-1}px`,c))}),E(),()=>{w.disconnect(),g.removeEventListener("scroll",E)}});let d;function v(f){ke[f?"unshift":"push"](()=>{o=f,t(1,o)})}function p(f){ke[f?"unshift":"push"](()=>{c=f,t(2,c)})}function m(f){ke[f?"unshift":"push"](()=>{r=f,t(0,r)})}return i.$$set=f=>{"session"in f&&t(5,s=f.session)},i.$$.update=()=>{i.$$.dirty&96&&t(3,d=Qt(s.rootFrame.cloneDeep(),n.processors,n.options))},[r,o,c,d,l,s,n,v,p,m]}class Rn extends fe{constructor(e){super(),de(this,e,Pn,Fn,re,{session:5})}}class In{constructor(e){A(this,"mediaQueryList",null);this.onDevicePixelRatioChanged=e,this._onChange=this._onChange.bind(this),this.createMediaQueryList()}createMediaQueryList(){this.removeMediaQueryList();let e=`(resolution: ${window.devicePixelRatio}dppx)`;this.mediaQueryList=matchMedia(e),this.mediaQueryList.addEventListener("change",this._onChange)}removeMediaQueryList(){var e;(e=this.mediaQueryList)==null||e.removeEventListener("change",this._onChange),this.mediaQueryList=null}_onChange(e){this.onDevicePixelRatioChanged(),this.createMediaQueryList()}destroy(){this.removeMediaQueryList()}}class Ln{constructor(e){A(this,"canvas");A(this,"_size_observer");A(this,"_devicePixelRatioObserver");A(this,"drawAnimationRequest",null);this.container=e,getComputedStyle(e).position!="absolute"&&(e.style.position="relative"),this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.left="0",this.canvas.style.top="0",this.canvas.style.width="100%",this.canvas.style.height="100%",this.container.appendChild(this.canvas),this.setCanvasSize=this.setCanvasSize.bind(this),this._size_observer=new ResizeObserver(this.setCanvasSize),this._size_observer.observe(e),this._devicePixelRatioObserver=new In(this.setCanvasSize),window.requestAnimationFrame(()=>{this.setCanvasSize()})}destroy(){this._size_observer.disconnect(),this._devicePixelRatioObserver.destroy(),this.canvas.remove(),this.drawAnimationRequest!==null&&(window.cancelAnimationFrame(this.drawAnimationRequest),this.drawAnimationRequest=null)}setNeedsRedraw(){this.drawAnimationRequest===null&&(this.drawAnimationRequest=window.requestAnimationFrame(()=>{this.drawAnimationRequest=null,this.canvasViewRedraw()}))}redrawIfNeeded(){this.drawAnimationRequest!==null&&(window.cancelAnimationFrame(this.drawAnimationRequest),this.drawAnimationRequest=null,this.canvasViewRedraw())}canvasViewRedraw(){const e=this.canvas.getContext("2d");e&&(e.resetTransform(),e.scale(window.devicePixelRatio,window.devicePixelRatio),this.redraw(e,{width:this.canvas.width/window.devicePixelRatio,height:this.canvas.height/window.devicePixelRatio}))}get width(){return this.canvas.width/window.devicePixelRatio}get height(){return this.canvas.height/window.devicePixelRatio}setCanvasSize(){const e=window.devicePixelRatio;this.canvas.height=this.container.clientHeight*e,this.canvas.width=this.container.clientWidth*e,this.canvasViewRedraw()}}function Sn(i){let e,t=i[2]=="self"?"self":"time",n,s,l,r=i[3](i[0].time)+"";return{c(){e=h("div"),n=I(t),s=b(),l=h("div"),a(e,"class","label svelte-ci3g2p"),a(l,"class","time-val svelte-ci3g2p")},m(o,c){S(o,e,c),u(e,n),S(o,s,c),S(o,l,c),l.innerHTML=r},p(o,c){c&4&&t!==(t=o[2]=="self"?"self":"time")&&_e(n,t),c&1&&r!==(r=o[3](o[0].time)+"")&&(l.innerHTML=r)},d(o){o&&(L(e),L(s),L(l))}}}function Dn(i){let e,t,n,s,l=i[3](i[0].time)+"",r,o=i[0].selfTime/i[0].time>.001&&ri(i);return{c(){e=h("div"),e.textContent="time",t=b(),n=h("div"),s=h("div"),r=b(),o&&o.c(),a(e,"class","label svelte-ci3g2p"),a(s,"class","time-val svelte-ci3g2p"),a(n,"class","time-row svelte-ci3g2p")},m(c,d){S(c,e,d),S(c,t,d),S(c,n,d),u(n,s),s.innerHTML=l,u(n,r),o&&o.m(n,null)},p(c,d){d&1&&l!==(l=c[3](c[0].time)+"")&&(s.innerHTML=l),c[0].selfTime/c[0].time>.001?o?o.p(c,d):(o=ri(c),o.c(),o.m(n,null)):o&&(o.d(1),o=null)},d(c){c&&(L(e),L(t),L(n)),o&&o.d()}}}function ri(i){let e,t,n,s=i[3](i[0].selfTime)+"";return{c(){e=h("div"),e.textContent="self",t=b(),n=h("div"),a(e,"class","label svelte-ci3g2p"),a(n,"class","time-val svelte-ci3g2p")},m(l,r){S(l,e,r),S(l,t,r),S(l,n,r),n.innerHTML=s},p(l,r){r&1&&s!==(s=l[3](l[0].selfTime)+"")&&(n.innerHTML=s)},d(l){l&&(L(e),L(t),L(n))}}}function Hn(i){let e,t,n=i[0].name+"",s,l,r,o,c,d,v,p,m,f;function g(C,y){return C[2]=="both"?Dn:Sn}let w=g(i),E=w(i);return{c(){e=h("div"),t=h("div"),s=I(n),l=b(),E.c(),r=b(),o=h("div"),o.textContent="loc",c=b(),d=h("div"),v=h("div"),m=b(),f=new Ri(!1),a(t,"class","name svelte-ci3g2p"),a(o,"class","label svelte-ci3g2p"),a(v,"class","location-color svelte-ci3g2p"),a(v,"style",p=`background: ${i[0].locationColor}`),f.a=null,a(d,"class","location-row"),a(e,"class","timeline-canvas-view-tooltip svelte-ci3g2p"),a(e,"style",`font: ${ai}; max-width: ${Vn}px;`)},m(C,y){S(C,e,y),u(e,t),u(t,s),u(e,l),E.m(e,null),u(e,r),u(e,o),u(e,c),u(e,d),u(d,v),u(d,m),f.m(i[1],d)},p(C,[y]){y&1&&n!==(n=C[0].name+"")&&_e(s,n),w===(w=g(C))&&E?E.p(C,y):(E.d(1),E=w(C),E&&(E.c(),E.m(e,r))),y&1&&p!==(p=`background: ${C[0].locationColor}`)&&a(v,"style",p),y&2&&f.p(C[1])},i:F,o:F,d(C){C&&L(e),E.d()}}}function li(i){return i.selfTime==i.time?"self":i.selfTime/i.time>.001?"both":"time"}function On(i,e){i.font=ai;const t=li(e)=="both"?140:70,n=i.measureText(e.name).width,s=i.measureText(e.location).width+46;let r=Math.max(t,n,s)+20;return r>310&&(r=310),r}const Vn=310,ai="400 13px Source Sans Pro, sans-serif";function Nn(i,e,t){let{f:n}=e,s,l;function r(o){return`<span style="color: ${zt(o/n.totalTime)}">${o.toFixed(3)}</span>`}return i.$$set=o=>{"f"in o&&t(0,n=o.f)},i.$$.update=()=>{i.$$.dirty&1&&t(1,s=Ct(n.location)),i.$$.dirty&1&&t(2,l=li(n))},[n,s,l,r]}class xn extends fe{constructor(e){super(),de(this,e,Nn,Hn,re,{f:0})}}const $n="#212325",ci=18,Bn=17,Ie=28,lt=17,ui=29,zn=["#3475BA","#318DBC","#47A298","#8AAE5D","#C1A731","#C07210","#B84210","#B53134","#9A3586","#4958B5","#3475BA"].map(Ki);class Wn extends Ln{constructor(t){super(t);A(this,"zoom",1);A(this,"startT",0);A(this,"yOffset",0);A(this,"frames",[]);A(this,"isZoomedIn",!1);A(this,"tooltipContainer");A(this,"tooltipComponent",null);A(this,"_rootFrame",null);A(this,"maxDepth",0);A(this,"tooltipLocation",null);A(this,"lastDrawWidth",0);A(this,"lastDrawHeight",0);A(this,"_libraryOrder",null);A(this,"_colors",[]);A(this,"_frameMaxT");A(this,"mouseLocation",null);A(this,"mouseDownLocation",null);A(this,"touches",{});this.onWheel=this.onWheel.bind(this),this.onMouseMove=this.onMouseMove.bind(this),this.onMouseLeave=this.onMouseLeave.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.windowMouseUp=this.windowMouseUp.bind(this),this.onTouchstart=this.onTouchstart.bind(this),this.onTouchmove=this.onTouchmove.bind(this),this.onTouchend=this.onTouchend.bind(this),this.onTouchcancel=this.onTouchend.bind(this),this.canvas.addEventListener("wheel",this.onWheel),this.canvas.addEventListener("mousemove",this.onMouseMove),this.canvas.addEventListener("mouseleave",this.onMouseLeave),this.canvas.addEventListener("mousedown",this.onMouseDown),this.canvas.addEventListener("touchstart",this.onTouchstart),this.canvas.addEventListener("touchmove",this.onTouchmove),this.canvas.addEventListener("touchend",this.onTouchend),this.canvas.addEventListener("touchcancel",this.onTouchcancel),this.tooltipContainer=document.createElement("div"),this.tooltipContainer.style.position="absolute",this.tooltipContainer.style.pointerEvents="none",this.container.appendChild(this.tooltipContainer)}destroy(){this.canvas.removeEventListener("wheel",this.onWheel),this.canvas.removeEventListener("mousemove",this.onMouseMove),this.canvas.removeEventListener("mouseleave",this.onMouseLeave),this.canvas.removeEventListener("mousedown",this.onMouseDown),this.canvas.removeEventListener("touchstart",this.onTouchstart),this.canvas.removeEventListener("touchmove",this.onTouchmove),this.canvas.removeEventListener("touchend",this.onTouchend),this.canvas.removeEventListener("touchcancel",this.onTouchcancel),this.tooltipContainer.remove(),super.destroy()}setRootFrame(t){this._rootFrame=t,this.frames=[],this._frameMaxT=void 0,this.maxDepth=0,this._collectFrames(t,0),this.fitContents(),this.setNeedsRedraw()}_collectFrames(t,n){this.frames.push({frame:t,depth:n,isApplicationCode:t.isApplicationCode,library:t.library,className:t.className,filePathShort:t.filePathShort}),this.maxDepth=Math.max(this.maxDepth,n);for(const s of t.children)s.identifier!==Ze&&this._collectFrames(s,n+1)}updateTooltip(t,n){var s;if(n){const l={name:this.frameName(n),time:n.frame.time,selfTime:this.frameSelfTime(n),totalTime:((s=this._rootFrame)==null?void 0:s.time)??1e-12,location:`${n.filePathShort}:${n.frame.lineNo}`,locationColor:this.colorForFrame(n)};if(this.tooltipComponent?this.tooltipComponent.$set({f:l}):this.tooltipComponent=new xn({target:this.tooltipContainer,props:{f:l}}),this.tooltipLocation){const r={x:this.tooltipLocation.x+12,y:this.tooltipLocation.y+12},o=On(t,l),c=this.width-10-o;r.x>c&&(r.x=c);const v=this.height-10-60;r.y>v&&(r.y=v),this.tooltipContainer.style.left=`${r.x}px`,this.tooltipContainer.style.top=`${r.y}px`}}n||this.tooltipComponent&&(this.tooltipComponent.$destroy(),this.tooltipComponent=null)}redraw(t,n){const{width:s,height:l}=n;(s!==this.lastDrawWidth||l!==this.lastDrawHeight)&&(this.isZoomedIn?this.clampViewport():this.fitContents()),this.lastDrawWidth=s,this.lastDrawHeight=l,t.fillStyle=$n,t.fillRect(0,0,s,l),this.drawAxes(t);for(const d of this.frames)this.drawFrame(t,d);t.globalAlpha=1;const r=this.maxYOffset>0||this.isZoomedIn,o=!!this.mouseDownLocation;this.canvas.style.cursor=o&&r?"grabbing":"initial",t.fillStyle="red",t.font='23px "Source Sans Pro", sans-serif';let c=null;!o&&this.tooltipLocation&&(c=this.hitTest(this.tooltipLocation)),this.updateTooltip(t,c)}drawAxes(t){const n=Math.max(800,this.width)/this.zoom;if(n==0)return;const s=Math.log10(n);let l=Math.ceil(s)+2;l<0&&(l=0);const r=Math.ceil(s)-3,o=c=>xe(c,{from:[s,s-3],to:[.71,0],clamp:!0});for(let c=r;c<l;c++){let d=o(c);d=Math.max(0,Math.min(1,d)),d=Math.pow(d,2),this.drawAxis(t,Math.pow(10,c),d)}this.drawAxis(t,Math.pow(10,l),o(l),!0)}drawAxis(t,n,s,l=!1){t.fillStyle="white";const r=Math.floor(this.startT/n)*n,o=this.startT+this.width/this.zoom,c=Math.max(0,Math.ceil(-Math.log10(n)));for(let d=r;d<o;d+=n){const v=this.xForT(d);if(Math.round(d/n)%10===0&&!l)continue;t.globalAlpha=s;const m=lt-this.yOffset;t.fillRect(v,m,1,this.height-m);const f=xe(s,{from:[.12,.25],to:[0,.5],clamp:!0});if(f>.01){t.globalAlpha=f,t.font='13px "Source Sans Pro", sans-serif';let g=d.toFixed(c);g=="0"&&(g="0s");let w=m+10;t.fillText(g,v+3,w);let E=this.height+lt+10-this.yOffset;E<this.height-3&&(E=this.height-3),t.fillText(g,v+3,E)}t.globalAlpha=1}}drawFrame(t,n){const{x:s,y:l,w:r,h:o}=this.frameDims(n);if(s+r<0||s>this.width)return;if(t.fillStyle=this.colorForFrame(n),t.globalAlpha=n.isApplicationCode?1:.5,r<2){t.fillRect(s,l,r,o);return}let d=this.frameName(n);const v=Math.floor(r/3.3);if(d.length>v&&(d=d.substring(0,v)),d.length==0){t.fillRect(s,l,r,o);return}t.save(),t.beginPath(),t.rect(s,l,r,o),t.fill(),t.clip(),t.font='13px "Source Sans Pro", sans-serif',t.fillStyle="white";let p=s;p<0&&(p=0),t.fillText(d,p+2,l+13),t.restore()}_assignLibraryOrder(){const t={};for(const s of this.frames){const r=s.frame.library??"";t[r]=(t[r]||0)+s.frame.time}const n=Object.keys(t);n.sort((s,l)=>t[l]-t[s]),this._libraryOrder=n}colorForLibraryIndex(t){if(this._colors[t]!==void 0)return this._colors[t];const n=Math.pow(2,Math.ceil(Math.log2(t+1))),l=(2*t-n+1)/n,r=Gi(zn,l);return this._colors[t]=r,r}libraryIndexForFrame(t){this._libraryOrder||this._assignLibraryOrder();const n=t.library||"";let s=this._libraryOrder.indexOf(n);return s===-1&&(s=this._libraryOrder.length,this._libraryOrder.push(n)),s}colorForFrame(t){const n=this.libraryIndexForFrame(t);return this.colorForLibraryIndex(n)}get frameMaxT(){return this._frameMaxT===void 0&&(this._frameMaxT=this.frames.reduce((t,n)=>Math.max(t,n.frame.startTime+n.frame.time),0)),this._frameMaxT}get maxYOffset(){return Math.max(0,(this.maxDepth+1)*ci+lt*2+ui-this.height)}get minZoom(){return(this.width-2*Ie)/this.frameMaxT}get maxZoom(){return 6666666666666667e-8}fitContents(){this.startT=0,this.zoom=this.minZoom,this.isZoomedIn=!1}clampViewport(){this.zoom<this.minZoom?(this.zoom=this.minZoom,this.isZoomedIn=!1):this.isZoomedIn=!0,this.zoom>this.maxZoom&&(this.zoom=this.maxZoom),this.startT<0&&(this.startT=0);const t=this.frameMaxT-(this.width-2*Ie)/this.zoom;this.startT>t&&(this.startT=t),this.yOffset<0&&(this.yOffset=0),this.yOffset>this.maxYOffset&&(this.yOffset=this.maxYOffset)}frameDims(t){const n=t.depth*ci+lt+ui-this.yOffset,s=Bn;let l=this.xForT(t.frame.startTime),o=this.xForT(t.frame.startTime+t.frame.time)-l;return o<1&&(o=1),o>1&&(o-=xe(o,{from:[1,3],to:[0,1],clamp:!0})),{x:l,y:n,w:o,h:s}}xForT(t){return(t-this.startT)*this.zoom+Ie}tForX(t){return(t-Ie)/this.zoom+this.startT}frameName(t){let n;return t.className?n=`${t.className}.${t.frame.function}`:t.frame.function=="<module>"?n=t.filePathShort??t.frame.filePath??"":n=t.frame.function,n}frameSelfTime(t){let n=t.frame.time;const s=t.frame.children.filter(l=>!l.isSynthetic);for(const l of s)n-=l.time;return n}hitTest(t){for(const n of this.frames){const{x:s,y:l,w:r,h:o}=this.frameDims(n);if(t.x>=s&&t.x<=s+r&&t.y>=l&&t.y<=l+o)return n}return null}onWheel(t){const n=t.ctrlKey||t.metaKey,s=n?.01:.0023,l=this.tForX(t.offsetX);this.zoom*=1-t.deltaY*s,this.clampViewport(),this.startT=l-(t.offsetX-Ie)/this.zoom,n||(this.startT+=t.deltaX/this.zoom),this.clampViewport(),this.setNeedsRedraw(),t.preventDefault()}onMouseMove(t){const n={x:t.offsetX,y:t.offsetY},s=this.mouseLocation;if(this.mouseLocation=n,s&&this.mouseDownLocation){const l={x:n.x-s.x,y:n.y-s.y};this.startT-=l.x/this.zoom,this.yOffset-=l.y,this.clampViewport()}this.tooltipLocation=n,this.setNeedsRedraw()}onMouseLeave(t){this.mouseLocation=null,this.tooltipLocation=null,this.setNeedsRedraw()}onMouseDown(t){(t.button===0||t.button===1)&&(this.mouseDownLocation={x:t.offsetX,y:t.offsetY},window.addEventListener("mouseup",this.windowMouseUp),this.setNeedsRedraw())}windowMouseUp(t){window.removeEventListener("mouseup",this.windowMouseUp),this.mouseDownLocation=null,this.setNeedsRedraw()}onTouchstart(t){t.preventDefault(),t.stopPropagation();for(const n of Array.from(t.changedTouches))this.touches[n.identifier]={x:n.clientX,y:n.clientY,downT:this.tForX(n.clientX),startDate:Date.now(),downX:n.clientX,downY:n.clientY}}onTouchmove(t){t.preventDefault(),t.stopPropagation();let n=0;for(const l of Array.from(t.changedTouches)){const r=this.touches[l.identifier];r&&(n+=l.clientY-r.y,this.touches[l.identifier]={...r,x:l.clientX,y:l.clientY})}const s=n/Object.keys(this.touches).length;this.yOffset-=s,this.adjustXAxisForTouches(),this.setNeedsRedraw()}onTouchend(t){t.preventDefault(),t.stopPropagation();for(const n of Array.from(t.changedTouches))delete this.touches[n.identifier];this.setNeedsRedraw()}onTouchcancel(t){t.preventDefault(),t.stopPropagation();for(const n of Array.from(t.changedTouches))delete this.touches[n.identifier];this.setNeedsRedraw()}adjustXAxisForTouches(){const t=Object.keys(this.touches).map(Number);if(t.length!=0){if(t.length==1){const n=this.touches[t[0]];this.startT=n.downT-(n.x-Ie)/this.zoom}if(t.length>=2){const n=this.touches[t[0]],s=this.touches[t[1]],l=(s.x-n.x)/(s.downT-n.downT),r=n.downT-(n.x-Ie)/l;this.startT=r,this.zoom=l}this.clampViewport()}}}function qn(i){let e;return{c(){e=h("div"),e.innerHTML="",a(e,"class","timeline svelte-p2tt1k")},m(t,n){S(t,e,n),i[6](e)},p:F,i:F,o:F,d(t){t&&L(e),i[6](null)}}}function Un(i,e,t){let n,{session:s}=e;const l=Vt([je],([v])=>({processors:[v.removeImportlib?Ft:null,v.removeTracebackHide?Pt:null,v.removePyinstrument?si:null].filter(f=>f!==null),options:{}}));ge(i,l,v=>t(5,n=v));let r,o=null,c=null;Ii(()=>{c==null||c.destroy()});function d(v){ke[v?"unshift":"push"](()=>{o=v,t(0,o)})}return i.$$set=v=>{"session"in v&&t(2,s=v.session)},i.$$.update=()=>{i.$$.dirty&36&&t(3,r=Qt(s.rootFrame.cloneDeep(),n.processors,n.options)),i.$$.dirty&1&&o&&t(4,c=new Wn(o)),i.$$.dirty&24&&r&&c&&c.setRootFrame(r)},[o,l,s,r,c,n,d]}class Yn extends fe{constructor(e){super(),de(this,e,Un,qn,re,{session:2})}}function Xn(i){let e,t,n=i[1].viewMode+"",s;return{c(){e=h("div"),t=I("Unknown view mode: "),s=I(n),a(e,"class","error")},m(l,r){S(l,e,r),u(e,t),u(e,s)},p(l,r){r&2&&n!==(n=l[1].viewMode+"")&&_e(s,n)},i:F,o:F,d(l){l&&L(e)}}}function Gn(i){let e,t;return e=new Yn({props:{session:i[0]}}),{c(){we(e.$$.fragment)},m(n,s){ce(e,n,s),t=!0},p(n,s){const l={};s&1&&(l.session=n[0]),e.$set(l)},i(n){t||(D(e.$$.fragment,n),t=!0)},o(n){x(e.$$.fragment,n),t=!1},d(n){ue(e,n)}}}function jn(i){let e,t;return e=new Rn({props:{session:i[0]}}),{c(){we(e.$$.fragment)},m(n,s){ce(e,n,s),t=!0},p(n,s){const l={};s&1&&(l.session=n[0]),e.$set(l)},i(n){t||(D(e.$$.fragment,n),t=!0)},o(n){x(e.$$.fragment,n),t=!1},d(n){ue(e,n)}}}function Zn(i){let e;return{c(){e=h("div"),e.innerHTML='<div class="spacer" style="height: 20px;"></div> <div class="error">No samples recorded.</div>',a(e,"class","margins")},m(t,n){S(t,e,n)},p:F,i:F,o:F,d(t){t&&L(e)}}}function Kn(i){let e,t,n,s,l,r,o,c;n=new pn({props:{session:i[0]}});const d=[Zn,jn,Gn,Xn],v=[];function p(m,f){return m[0].rootFrame?m[1].viewMode==="call-stack"?1:m[1].viewMode==="timeline"?2:3:0}return r=p(i),o=v[r]=d[r](i),{c(){e=h("div"),t=h("div"),we(n.$$.fragment),s=b(),l=h("div"),o.c(),a(t,"class","header"),a(l,"class","body svelte-1vwroj7"),a(e,"class","app svelte-1vwroj7")},m(m,f){S(m,e,f),u(e,t),ce(n,t,null),u(e,s),u(e,l),v[r].m(l,null),c=!0},p(m,[f]){const g={};f&1&&(g.session=m[0]),n.$set(g);let w=r;r=p(m),r===w?v[r].p(m,f):(Oe(),x(v[w],1,1,()=>{v[w]=null}),Ve(),o=v[r],o?o.p(m,f):(o=v[r]=d[r](m),o.c()),D(o,1),o.m(l,null))},i(m){c||(D(n.$$.fragment,m),D(o),c=!0)},o(m){x(n.$$.fragment,m),x(o),c=!1},d(m){m&&L(e),ue(n),v[r].d()}}}function Qn(i,e,t){let n;ge(i,Ge,p=>t(1,n=p));let{session:s}=e;const l=document.createElement("link");l.rel="shortcut icon",l.href=vn,document.head.appendChild(l);const r=document.createElement("link");r.rel="preload",r.as="style",r.onload=()=>{r.rel="stylesheet"},r.href="https://fonts.googleapis.com/css?family=Source+Code+Pro:400,600|Source+Sans+Pro:400,600&display=swap",document.head.appendChild(r);const o=s.rootFrame,c=o==null?void 0:o.time.toLocaleString(void 0,{maximumSignificantDigits:3});let d,v;return(v=/[^\s/]+(:\d+)?$/.exec(s.target_description))?d=v[0]:d=s.target_description,document.title=`${c}s - ${d} - pyinstrument`,i.$$set=p=>{"session"in p&&t(0,s=p.session)},[s,n]}class Jn extends fe{constructor(e){super(),de(this,e,Qn,Kn,re,{session:0})}}class es{constructor(e){A(this,"startTime");A(this,"duration");A(this,"minInterval");A(this,"maxInterval");A(this,"sampleCount");A(this,"target_description");A(this,"cpuTime");A(this,"rootFrame");A(this,"sysPath");A(this,"sysPrefixes");A(this,"_shortenPathCache",{});this.startTime=e.session.start_time,this.duration=e.session.duration,this.minInterval=e.session.min_interval,this.maxInterval=e.session.max_interval,this.sampleCount=e.session.sample_count,this.target_description=e.session.target_description,this.cpuTime=e.session.cpu_time,this.sysPath=e.session.sys_path,this.sysPrefixes=e.session.sys_prefixes,this.rootFrame=new Ke(e.frame_tree,this)}shortenPath(e){if(this._shortenPathCache[e])return this._shortenPathCache[e];let t=e;if($e(e).length>1)for(const s of this.sysPath){const l=ts(e,s);$e(l).length<$e(t).length&&(t=l)}return this._shortenPathCache[e]=t,t}}function $e(i){return i.split(/[/\\]/)}function di(i){const e=$e(i);return e.length>0&&e[0].endsWith(":")?e[0]:null}function ts(i,e){if(di(i)!=di(e))return i;const t=$e(i),n=$e(e);let s=0;for(;s<t.length&&s<n.length&&t[s]==n[s];)s++;return n.slice(s).map(r=>"..").concat(t.slice(s)).join("/")}return{render(i,e){const t=new es(e);return new Jn({target:i,props:{session:t}})}}}();
</script>
                <style>html,body{background-color:#303538;color:#fff;padding:0;margin:0}.margins{padding:0 30px}label{-webkit-user-select:none;user-select:none}label *{-webkit-user-select:initial;user-select:initial}.view-options-call-stack.svelte-1pecl4m.svelte-1pecl4m{padding:6px 9px}.option.svelte-1pecl4m.svelte-1pecl4m{display:grid;grid-template-columns:auto 1fr;align-items:start;padding-left:1px;margin-bottom:3px}.option.svelte-1pecl4m .description.svelte-1pecl4m{font-size:12px;color:#999;grid-column:2/3}.option-group.svelte-1pecl4m.svelte-1pecl4m{margin-bottom:10px}.option-group.svelte-1pecl4m .name.svelte-1pecl4m{margin-bottom:4px}.mini-input-grid.svelte-1pecl4m.svelte-1pecl4m{display:grid;grid-template-columns:auto 1fr;gap:5px;align-items:baseline;margin-top:3px;margin-bottom:2px}.mini-input-grid.svelte-1pecl4m label.svelte-1pecl4m{font-weight:600}input.svelte-1pecl4m.svelte-1pecl4m{font-family:Source Code Pro,Roboto Mono,Consolas,Monaco,monospace;font-size-adjust:.486094;border-radius:3px;background:#4e5255;padding:1px 5px;font-size:12px;border:1px solid #4e5255;color:#ccc}input.svelte-1pecl4m.svelte-1pecl4m:focus-visible{outline:1px solid #abb2b7}input[type=number].svelte-1pecl4m.svelte-1pecl4m::-webkit-inner-spin-button{-webkit-appearance:none}.view-options-timeline.svelte-vsz8zm{padding:6px 9px}.view-options.svelte-rpk7lo{position:absolute;z-index:1;right:0}.box.svelte-rpk7lo{width:90vw;max-width:282px;height:max-content;max-height:calc(100vh - 100px);position:absolute;right:0;top:calc(100% + 4px);border-radius:5px;border:1px solid #4e5255;background:#2a2f32;box-shadow:0 2px 14px -5px #00000040;overflow:hidden;display:flex;flex-direction:column}.title-row.svelte-rpk7lo{padding:5px 9px;font-size:12px;font-weight:600;background-color:#3c4144}.body.svelte-rpk7lo{overflow-y:auto;flex-basis:content;flex-shrink:1}.header.svelte-qdxst2.svelte-qdxst2{background:#292f32;font-size:14px;padding:9px 0}.row.svelte-qdxst2.svelte-qdxst2{display:flex;align-items:center;gap:10px}.logo.svelte-qdxst2.svelte-qdxst2{margin:0 -3px 0 -6px}.layout.svelte-qdxst2.svelte-qdxst2{flex:1;display:grid;gap:0 10px;grid-template-columns:auto minmax(auto,max-content)}@media (max-width: 800px){.layout.svelte-qdxst2.svelte-qdxst2{grid-template-columns:1fr}}.target-description.svelte-qdxst2.svelte-qdxst2{font-weight:600;margin-bottom:1px}.view-options.svelte-qdxst2.svelte-qdxst2{display:flex;flex-wrap:wrap}.view-options.svelte-qdxst2 label.svelte-qdxst2{margin:0 5px;white-space:nowrap}.metrics.svelte-qdxst2.svelte-qdxst2{grid-row:span 2;text-align:right;align-items:end;min-width:min-content}@media (max-width: 800px){.metrics.svelte-qdxst2.svelte-qdxst2{text-align:left}.metrics.svelte-qdxst2 br.svelte-qdxst2{display:none}}.metric.svelte-qdxst2.svelte-qdxst2{display:inline-block;white-space:nowrap;margin-left:2px}@media (max-width: 800px){.metric.svelte-qdxst2.svelte-qdxst2{margin-left:0;margin-right:2px}}.metric-label.svelte-qdxst2.svelte-qdxst2{font-weight:600;color:#fff9}.metric-value.svelte-qdxst2.svelte-qdxst2{color:#fff6}input[type=radio].svelte-qdxst2.svelte-qdxst2{vertical-align:-8%}.button-container.svelte-qdxst2.svelte-qdxst2{position:relative}button.svelte-qdxst2.svelte-qdxst2{background:#5c6063;border-radius:6px;font:inherit;font-size:.8571428571em;color:inherit;border:none;cursor:pointer}button.svelte-qdxst2.svelte-qdxst2:hover{background:#63686b}button.svelte-qdxst2.svelte-qdxst2:active{background:#55585b}.frame.svelte-7e9kco.svelte-7e9kco{font-family:Source Code Pro,Roboto Mono,Consolas,Monaco,monospace;font-size-adjust:.486094;font-size:14px;z-index:0;position:relative;-webkit-user-select:none;user-select:none}.group-header.svelte-7e9kco.svelte-7e9kco{-webkit-user-select:none;user-select:none}.group-header-button.svelte-7e9kco.svelte-7e9kco{margin-left:35px;display:inline-block;color:#ffffff94;-webkit-user-select:none;user-select:none;cursor:default;position:relative}.group-header-button.svelte-7e9kco.svelte-7e9kco:before{position:absolute;left:-3px;right:-3px;top:0;bottom:0;content:"";z-index:-1;background-color:#3b4043}.group-header-button.svelte-7e9kco.svelte-7e9kco:hover:before{background-color:#4a4f54}.group-triangle.svelte-7e9kco.svelte-7e9kco,.frame-triangle.svelte-7e9kco.svelte-7e9kco{width:6px;height:10px;padding-left:6px;padding-right:5px;display:inline-block}.group-triangle.rotate.svelte-7e9kco.svelte-7e9kco,.frame-triangle.rotate.svelte-7e9kco.svelte-7e9kco{transform:translate(6px,4px) rotate(90deg)}.frame-description.svelte-7e9kco.svelte-7e9kco{display:flex;white-space:nowrap}.frame-description.svelte-7e9kco.svelte-7e9kco:hover{background-color:#35475980}.frame-description.svelte-7e9kco.svelte-7e9kco:focus-visible,.group-header.svelte-7e9kco.svelte-7e9kco:focus-visible{outline:none;background-color:#37516c}.frame-triangle.svelte-7e9kco.svelte-7e9kco{opacity:1}.frame-description.children-visible.svelte-7e9kco .frame-triangle.svelte-7e9kco{opacity:0}.frame-description.children-visible.svelte-7e9kco:hover .frame-triangle.svelte-7e9kco,.frame-description.children-visible.svelte-7e9kco:focus-visible .frame-triangle.svelte-7e9kco{opacity:1}.name.svelte-7e9kco.svelte-7e9kco,.time.svelte-7e9kco.svelte-7e9kco,.code-position.svelte-7e9kco.svelte-7e9kco{-webkit-user-select:text;user-select:text;cursor:default}.application-code.svelte-7e9kco .name.svelte-7e9kco{color:#5db3ff}.time.svelte-7e9kco.svelte-7e9kco{margin-right:.55em;color:#b8e98685}.code-position.svelte-7e9kco.svelte-7e9kco{color:#ffffff80;text-align:right;margin-left:2em}.visual-guide.svelte-7e9kco.svelte-7e9kco{top:21px;bottom:0;left:0;width:2px;background-color:#fff;position:absolute;opacity:.08;pointer-events:none}.frame-description:hover~.visual-guide.svelte-7e9kco.svelte-7e9kco{opacity:.4}.frame-description:hover~.children.svelte-7e9kco .visual-guide{opacity:.15}.call-stack-view.svelte-1hebm9u{background-color:#303538;position:absolute;top:0;bottom:0;left:0;right:0;overflow:auto}.call-stack-view.svelte-1hebm9u:focus{outline:none}.scroll-inner.svelte-1hebm9u{padding-top:10px;padding-bottom:40px;box-sizing:border-box;width:auto;min-width:max-content}.call-stack-margins.svelte-1hebm9u{padding-left:18px;padding-right:18px}.scroll-size-fixer.svelte-1hebm9u{height:1px;width:100px;position:absolute;left:0}.timeline-canvas-view-tooltip.svelte-ci3g2p.svelte-ci3g2p{box-sizing:border-box;width:max-content;border-radius:2px;border:1px solid rgba(255,255,255,.09);background:#202325;box-shadow:0 4px 4px #00000040;display:grid;grid-template-columns:minmax(auto,33px) minmax(auto,1fr);gap:1px 0;padding:4px 10px 7px;color:#fff}.timeline-canvas-view-tooltip.svelte-ci3g2p .name.svelte-ci3g2p{grid-column:span 2;line-break:anywhere}.timeline-canvas-view-tooltip.svelte-ci3g2p .label.svelte-ci3g2p{color:#ffffff80;margin-right:8px}.timeline-canvas-view-tooltip.svelte-ci3g2p .time-val.svelte-ci3g2p{margin-right:10px;font-weight:600}.timeline-canvas-view-tooltip.svelte-ci3g2p .time-row.svelte-ci3g2p{display:flex;justify-content:start}.timeline-canvas-view-tooltip.svelte-ci3g2p .location-color.svelte-ci3g2p{width:9px;height:9px;margin-right:3px;border-radius:2px;position:relative;display:inline-block}.timeline-canvas-view-tooltip.svelte-ci3g2p .location-color.svelte-ci3g2p:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border:1px solid #383838;mix-blend-mode:color-dodge;border-radius:2px}.timeline.svelte-p2tt1k{position:absolute;top:0;bottom:0;left:0;right:0;overflow:hidden;-webkit-user-select:none;user-select:none}.app.svelte-1vwroj7{font-family:Source Sans Pro,Arial,Helvetica,sans-serif;font-size-adjust:.486;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:flex;flex-direction:column;position:absolute;top:0;bottom:0;left:0;right:0}.body.svelte-1vwroj7{flex:1;position:relative}
</style>

                <script>
                    const sessionData = {"session": {"start_time": 1749981325.6353245, "duration": 0.041661739349365234, "min_interval": 0.001, "max_interval": 0.001, "sample_count": 15, "start_call_stack": ["AnyIO worker thread\u0000<thread>\u000018608", "_bootstrap\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\threading.py\u00001018\u0001cWorkerThread\u0001l1032", "_bootstrap_inner\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\threading.py\u00001058\u0001cWorkerThread\u0001l1075", "run\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\u0000953\u0001cWorkerThread\u0001l967", "wrapper\u0000C:\\D\\AI-Fin-Analyzer\\utils\\pyinstrument_profiler.py\u000057\u0001l60", "start\u0000C:\\D\\AI-Fin-Analyzer\\utils\\pyinstrument_profiler.py\u000024\u0001cSimpleProfiler\u0001l28"], "target_description": "Profile at C:\\D\\AI-Fin-Analyzer\\utils\\pyinstrument_profiler.py:28", "cpu_time": 0.015625, "sys_path": ["C:\\D\\AI-Fin-Analyzer", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\python312.zip", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\DLLs", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\win32", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\win32\\lib", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\Pythonwin"], "sys_prefixes": ["C:\\Users\\<USER>\\miniconda3\\envs\\myenv", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv", "C:\\Users\\<USER>\\miniconda3\\envs\\myenv"]}, "frame_tree": {"identifier": "wrapper\u0000C:\\D\\AI-Fin-Analyzer\\utils\\pyinstrument_profiler.py\u000057","time": 0.041952,"attributes": {"l60": 0.006839199999376433, "l63": 0.*****************, "l66": 0.001000199999907636},"children": [{"identifier": "start\u0000C:\\D\\AI-Fin-Analyzer\\utils\\pyinstrument_profiler.py\u000024","time": 0.006839,"attributes": {"cSimpleProfiler": 0.006839199999376433, "l29": 0.006839199999376433},"children": [{"identifier": "info\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00002208","time": 0.006839,"attributes": {"l2216": 0.006839199999376433},"children": [{"identifier": "info\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001529","time": 0.006839,"attributes": {"cRootLogger": 0.006839199999376433, "l1539": 0.006839199999376433},"children": [{"identifier": "_log\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001660","time": 0.006839,"attributes": {"cRootLogger": 0.006839199999376433, "l1684": 0.006839199999376433},"children": [{"identifier": "handle\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001686","time": 0.006839,"attributes": {"cRootLogger": 0.006839199999376433, "l1700": 0.006839199999376433},"children": [{"identifier": "callHandlers\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001746","time": 0.006839,"attributes": {"cRootLogger": 0.006839199999376433, "l1762": 0.006839199999376433},"children": [{"identifier": "handle\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001011","time": 0.006839,"attributes": {"cRotatingFileHandler": 0.006839199999376433, "l1028": 0.006839199999376433},"children": [{"identifier": "emit\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\handlers.py\u000065","time": 0.006839,"attributes": {"cRotatingFileHandler": 0.006839199999376433, "l75": 0.006839199999376433},"children": [{"identifier": "emit\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001266","time": 0.006839,"attributes": {"cRotatingFileHandler": 0.006839199999376433, "l1280": 0.006839199999376433},"children": [{"identifier": "emit\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001148","time": 0.006839,"attributes": {"cRotatingFileHandler": 0.006839199999376433, "l1168": 0.006839199999376433},"children": [{"identifier": "handleError\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001066","time": 0.006839,"attributes": {"cRotatingFileHandler": 0.006839199999376433, "l1082": 0.003548399999999674, "l1091": 0.0022630999992543366, "l1098": 0.0010277000001224224},"children": [{"identifier": "print_exception\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\traceback.py\u0000111","time": 0.003548,"attributes": {"l124": 0.0010225000005448237, "l125": 0.0025258999994548503},"children": [{"identifier": "[self]","time": 0.001023,"attributes": {},"children": []},{"identifier": "print\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\traceback.py\u00001045","time": 0.002526,"attributes": {"cTracebackException": 0.0025258999994548503, "l1050": 0.0025258999994548503},"children": [{"identifier": "print\u0000<built-in>\u00000","time": 0.002526,"attributes": {},"children": [{"identifier": "[self]","time": 0.001190,"attributes": {},"children": []},{"identifier": "[self]","time": 0.001336,"attributes": {},"children": []}]}]}]},{"identifier": "print_stack\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\traceback.py\u0000202","time": 0.002263,"attributes": {"l211": 0.0022630999992543366},"children": [{"identifier": "extract_stack\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\traceback.py\u0000221","time": 0.001095,"attributes": {"l232": 0.001094899998861365},"children": [{"identifier": "extract\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\traceback.py\u0000377","time": 0.001095,"attributes": {"l395": 0.001094899998861365},"children": [{"identifier": "_extract_from_extended_frame_gen\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\traceback.py\u0000399","time": 0.001095,"attributes": {"l434": 0.001094899998861365},"children": [{"identifier": "checkcache\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\linecache.py\u000052","time": 0.001095,"attributes": {"l75": 0.001094899998861365},"children": [{"identifier": "stat\u0000<built-in>\u00000","time": 0.001095,"attributes": {},"children": [{"identifier": "[self]","time": 0.001095,"attributes": {},"children": []}]}]}]}]}]},{"identifier": "print_list\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\traceback.py\u000021","time": 0.001168,"attributes": {"l27": 0.0011682000003929716},"children": [{"identifier": "print\u0000<built-in>\u00000","time": 0.001168,"attributes": {},"children": [{"identifier": "[self]","time": 0.001168,"attributes": {},"children": []}]}]}]},{"identifier": "TextIOWrapper.write\u0000<built-in>\u00000","time": 0.001028,"attributes": {},"children": [{"identifier": "[self]","time": 0.001028,"attributes": {},"children": []}]}]}]}]}]}]}]}]}]}]}]}]},{"identifier": "analyse_documents\u0000C:\\D\\AI-Fin-Analyzer\\routers\\analyse_api.py\u000024","time": 0.034112,"attributes": {"l30": 0.*****************},"children": [{"identifier": "download_gdrive_folder_to_data_dir\u0000C:\\D\\AI-Fin-Analyzer\\utils\\gdrive_utils.py\u000045","time": 0.034112,"attributes": {"l55": 0.*****************},"children": [{"identifier": "__init__\u0000C:\\D\\AI-Fin-Analyzer\\utils\\gdrive_utils.py\u000011","time": 0.034112,"attributes": {"cGoogleDriveStrategy": 0.*****************, "l12": 0.026388500000393833, "l15": 0.007723700000497047},"children": [{"identifier": "from_service_account_file\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\google\\oauth2\\service_account.py\u0000248","time": 0.026389,"attributes": {"cCredentials": 0.026388500000393833, "l260": 0.026388500000393833},"children": [{"identifier": "from_filename\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\google\\auth\\_service_account_info.py\u000064","time": 0.026389,"attributes": {"l80": 0.026388500000393833},"children": [{"identifier": "from_dict\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\google\\auth\\_service_account_info.py\u000024","time": 0.026389,"attributes": {"l57": 0.026388500000393833},"children": [{"identifier": "from_service_account_info\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\google\\auth\\crypt\\base.py\u000089","time": 0.026389,"attributes": {"cRSASigner": 0.026388500000393833, "l109": 0.026388500000393833},"children": [{"identifier": "from_string\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\google\\auth\\crypt\\_cryptography_rsa.py\u0000114","time": 0.026389,"attributes": {"cRSASigner": 0.026388500000393833, "l133": 0.026388500000393833},"children": [{"identifier": "load_pem_private_key\u0000<built-in>\u00000","time": 0.026389,"attributes": {},"children": [{"identifier": "[self]","time": 0.026389,"attributes": {},"children": []}]}]}]}]}]}]},{"identifier": "positional_wrapper\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\googleapiclient\\_helpers.py\u0000111","time": 0.007724,"attributes": {"l130": 0.007723700000497047},"children": [{"identifier": "build\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\googleapiclient\\discovery.py\u0000192","time": 0.007724,"attributes": {"l304": 0.004461299999093171, "l315": 0.0032624000014038756},"children": [{"identifier": "_retrieve_discovery_doc\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\googleapiclient\\discovery.py\u0000372","time": 0.004461,"attributes": {"l404": 0.0011983000003965572, "l408": 0.0021772000000055414, "l417": 0.0010857999986910727},"children": [{"identifier": "_handle_fromlist\u0000<frozen importlib._bootstrap>\u00001390","time": 0.001198,"attributes": {"l1415": 0.0011983000003965572},"children": [{"identifier": "_call_with_frames_removed\u0000<frozen importlib._bootstrap>\u0000480","time": 0.001198,"attributes": {"l488": 0.0011983000003965572},"children": [{"identifier": "_find_and_load\u0000<frozen importlib._bootstrap>\u00001349","time": 0.001198,"attributes": {"l1360": 0.0011983000003965572},"children": [{"identifier": "_find_and_load_unlocked\u0000<frozen importlib._bootstrap>\u00001304","time": 0.001198,"attributes": {"l1331": 0.0011983000003965572},"children": [{"identifier": "_load_unlocked\u0000<frozen importlib._bootstrap>\u0000911","time": 0.001198,"attributes": {"l935": 0.0011983000003965572},"children": [{"identifier": "exec_module\u0000<frozen importlib._bootstrap_external>\u0000993","time": 0.001198,"attributes": {"cSourceFileLoader": 0.0011983000003965572, "l995": 0.0011983000003965572},"children": [{"identifier": "get_code\u0000<frozen importlib._bootstrap_external>\u00001066","time": 0.001198,"attributes": {"cSourceFileLoader": 0.0011983000003965572, "l1091": 0.0011983000003965572},"children": [{"identifier": "get_data\u0000<frozen importlib._bootstrap_external>\u00001187","time": 0.001198,"attributes": {"cSourceFileLoader": 0.0011983000003965572, "l1190": 0.0011983000003965572},"children": [{"identifier": "open_code\u0000<built-in>\u00000","time": 0.001198,"attributes": {},"children": [{"identifier": "[self]","time": 0.001198,"attributes": {},"children": []}]}]}]}]}]}]}]}]}]},{"identifier": "autodetect\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\__init__.py\u000030","time": 0.002177,"attributes": {"l45": 0.0011787000003096182, "l49": 0.0009984999996959232},"children": [{"identifier": "_handle_fromlist\u0000<frozen importlib._bootstrap>\u00001390","time": 0.001179,"attributes": {"l1415": 0.0011787000003096182},"children": [{"identifier": "_call_with_frames_removed\u0000<frozen importlib._bootstrap>\u0000480","time": 0.001179,"attributes": {"l488": 0.0011787000003096182},"children": [{"identifier": "_find_and_load\u0000<frozen importlib._bootstrap>\u00001349","time": 0.001179,"attributes": {"l1360": 0.0011787000003096182},"children": [{"identifier": "_find_and_load_unlocked\u0000<frozen importlib._bootstrap>\u00001304","time": 0.001179,"attributes": {"l1331": 0.0011787000003096182},"children": [{"identifier": "_load_unlocked\u0000<frozen importlib._bootstrap>\u0000911","time": 0.001179,"attributes": {"l935": 0.0011787000003096182},"children": [{"identifier": "exec_module\u0000<frozen importlib._bootstrap_external>\u0000993","time": 0.001179,"attributes": {"cSourceFileLoader": 0.0011787000003096182, "l995": 0.0011787000003096182},"children": [{"identifier": "get_code\u0000<frozen importlib._bootstrap_external>\u00001066","time": 0.001179,"attributes": {"cSourceFileLoader": 0.0011787000003096182, "l1091": 0.0011787000003096182},"children": [{"identifier": "get_data\u0000<frozen importlib._bootstrap_external>\u00001187","time": 0.001179,"attributes": {"cSourceFileLoader": 0.0011787000003096182, "l1190": 0.0011787000003096182},"children": [{"identifier": "open_code\u0000<built-in>\u00000","time": 0.001179,"attributes": {},"children": [{"identifier": "[self]","time": 0.001179,"attributes": {},"children": []}]}]}]}]}]}]}]}]}]},{"identifier": "info\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001529","time": 0.000998,"attributes": {"cLogger": 0.0009984999996959232, "l1539": 0.0009984999996959232},"children": [{"identifier": "_log\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001660","time": 0.000998,"attributes": {"cLogger": 0.0009984999996959232, "l1684": 0.0009984999996959232},"children": [{"identifier": "handle\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001686","time": 0.000998,"attributes": {"cLogger": 0.0009984999996959232, "l1700": 0.0009984999996959232},"children": [{"identifier": "callHandlers\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001746","time": 0.000998,"attributes": {"cLogger": 0.0009984999996959232, "l1762": 0.0009984999996959232},"children": [{"identifier": "handle\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\logging\\__init__.py\u00001011","time": 0.000998,"attributes": {"cRotatingFileHandler": 0.0009984999996959232, "l1028": 0.0009984999996959232},"children": [{"identifier": "[self]","time": 0.000998,"attributes": {},"children": []}]}]}]}]}]}]},{"identifier": "get_static_doc\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\__init__.py\u000055","time": 0.001086,"attributes": {"l73": 0.0010857999986910727},"children": [{"identifier": "[self]","time": 0.001086,"attributes": {},"children": []}]}]},{"identifier": "positional_wrapper\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\googleapiclient\\_helpers.py\u0000111","time": 0.003262,"attributes": {"l130": 0.0032624000014038756},"children": [{"identifier": "build_from_document\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\googleapiclient\\discovery.py\u0000463","time": 0.003262,"attributes": {"l556": 0.0032624000014038756},"children": [{"identifier": "loads\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\json\\__init__.py\u0000299","time": 0.003262,"attributes": {"l346": 0.0032624000014038756},"children": [{"identifier": "decode\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\json\\decoder.py\u0000333","time": 0.003262,"attributes": {"cJSONDecoder": 0.0032624000014038756, "l338": 0.0032624000014038756},"children": [{"identifier": "raw_decode\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\json\\decoder.py\u0000344","time": 0.003262,"attributes": {"cJSONDecoder": 0.0032624000014038756, "l354": 0.0032624000014038756},"children": [{"identifier": "[self]","time": 0.001263,"attributes": {},"children": []},{"identifier": "_warn_unawaited_coroutine\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\warnings.py\u0000536","time": 0.002000,"attributes": {"l555": 0.001999600000999635},"children": [{"identifier": "_showwarnmsg\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\warnings.py\u000099","time": 0.002000,"attributes": {"l115": 0.001999600000999635},"children": [{"identifier": "_showwarnmsg_impl\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\warnings.py\u000020","time": 0.002000,"attributes": {"l28": 0.001999600000999635},"children": [{"identifier": "_formatwarnmsg\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\warnings.py\u0000120","time": 0.002000,"attributes": {"l131": 0.001999600000999635},"children": [{"identifier": "_formatwarnmsg_impl\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\warnings.py\u000035","time": 0.002000,"attributes": {"l58": 0.001999600000999635},"children": [{"identifier": "_find_and_load\u0000<frozen importlib._bootstrap>\u00001349","time": 0.002000,"attributes": {"l1360": 0.001999600000999635},"children": [{"identifier": "_find_and_load_unlocked\u0000<frozen importlib._bootstrap>\u00001304","time": 0.002000,"attributes": {"l1322": 0.000999500000034459, "l1331": 0.0010001000009651762},"children": [{"identifier": "_find_spec\u0000<frozen importlib._bootstrap>\u00001240","time": 0.001000,"attributes": {"l1262": 0.000999500000034459},"children": [{"identifier": "find_spec\u0000<frozen importlib._bootstrap_external>\u00001524","time": 0.001000,"attributes": {"cPathFinder": 0.000999500000034459, "l1532": 0.000999500000034459},"children": [{"identifier": "_get_spec\u0000<frozen importlib._bootstrap_external>\u00001495","time": 0.001000,"attributes": {"cPathFinder": 0.000999500000034459, "l1506": 0.000999500000034459},"children": [{"identifier": "find_spec\u0000<frozen importlib._bootstrap_external>\u00001597","time": 0.001000,"attributes": {"cFileFinder": 0.000999500000034459, "l1633": 0.000999500000034459},"children": [{"identifier": "_path_join\u0000<frozen importlib._bootstrap_external>\u000096","time": 0.001000,"attributes": {"l108": 0.000999500000034459},"children": [{"identifier": "[self]","time": 0.001000,"attributes": {},"children": []}]}]}]}]}]},{"identifier": "_load_unlocked\u0000<frozen importlib._bootstrap>\u0000911","time": 0.001000,"attributes": {"l935": 0.0010001000009651762},"children": [{"identifier": "exec_module\u0000<frozen importlib._bootstrap_external>\u0000993","time": 0.001000,"attributes": {"cSourceFileLoader": 0.0010001000009651762, "l999": 0.0010001000009651762},"children": [{"identifier": "_call_with_frames_removed\u0000<frozen importlib._bootstrap>\u0000480","time": 0.001000,"attributes": {"l488": 0.0010001000009651762},"children": [{"identifier": "<module>\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\tracemalloc.py\u00001","time": 0.001000,"attributes": {"l311": 0.0010001000009651762},"children": [{"identifier": "[self]","time": 0.001000,"attributes": {},"children": []}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]},{"identifier": "stop\u0000C:\\D\\AI-Fin-Analyzer\\utils\\pyinstrument_profiler.py\u000031","time": 0.001000,"attributes": {"cSimpleProfiler": 0.001000199999907636, "l36": 0.001000199999907636},"children": [{"identifier": "stop\u0000C:\\Users\\<USER>\\miniconda3\\envs\\myenv\\Lib\\site-packages\\pyinstrument\\profiler.py\u0000168","time": 0.001000,"attributes": {"cProfiler": 0.001000199999907636, "l179": 0.001000199999907636},"children": [{"identifier": "[self]","time": 0.001000,"attributes": {},"children": []}]}]}]}};
                    pyinstrumentHTMLRenderer.render(document.getElementById('app'), sessionData);
                </script>
            </body>
            </html>
        