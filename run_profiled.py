# run_profiled.py
"""
Profiled version of the FastAPI application with VizTracer integration
"""
import uvicorn
import os
from viztracer import VizTracer
from datetime import datetime
from utils.profiling_utils import PerformanceProfiler
import logging

def setup_profiling_environment():
    """Setup profiling directories and logging"""
    os.makedirs("profiling_results", exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('profiling_results/profiling.log'),
            logging.StreamHandler()
        ]
    )

def run_with_viztracer():
    """Run the application with VizTracer profiling"""
    setup_profiling_environment()
    
    # Create unique trace file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    trace_file = f"profiling_results/full_app_trace_{timestamp}.json"
    
    print(f"🔍 Starting AI Financial Analyzer with VizTracer profiling...")
    print(f"📊 Trace will be saved to: {trace_file}")
    print(f"📈 Performance metrics will be saved to: profiling_results/")
    print(f"🌐 Access API docs at: http://localhost:8000/docs")
    
    # Initialize performance profiler
    profiler = PerformanceProfiler()
    profiler.start_monitoring()
    
    # Configure VizTracer
    tracer = VizTracer(
        output_file=trace_file,
        max_stack_depth=50,
        include_files=["*.py"],
        exclude_files=[
            "*/site-packages/*",
            "*/lib/python*/*",
            "*/venv/*",
            "*/env/*"
        ],
        ignore_c_function=True,
        ignore_frozen=True
    )
    
    try:
        tracer.start()
        
        # Use environment variable for host, default to localhost for local development
        import os
        host = os.getenv("HOST", "127.0.0.1")
        
        uvicorn.run(
            "routers.routes:app", 
            host=host, 
            port=8000, 
            reload=False,  # Disable reload for profiling
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Stopping profiler...")
    finally:
        tracer.stop()
        tracer.save()
        
        # Save final performance metrics
        final_metrics = profiler.stop_monitoring("full_application")
        print(f"\n📊 Final Performance Metrics:")
        print(f"   Duration: {final_metrics['duration_seconds']} seconds")
        print(f"   Memory Usage: {final_metrics['memory_end_mb']} MB")
        print(f"   Memory Delta: {final_metrics['memory_delta_mb']} MB")
        print(f"   CPU Usage: {final_metrics['cpu_percent']}%")
        print(f"\n✅ Profiling complete! Check {trace_file} for detailed trace.")

def run_without_profiling():
    """Run the application normally without profiling"""
    # Use environment variable for host, default to localhost for local development
    import os
    host = os.getenv("HOST", "127.0.0.1")
    
    uvicorn.run(
        "routers.routes:app", 
        host=host, 
        port=8000, 
        reload=True
    )

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--profile":
        run_with_viztracer()
    else:
        print("🚀 Starting AI Financial Analyzer (normal mode)")
        print("💡 Use --profile flag for profiling mode")
        print("   Example: python run_profiled.py --profile")
        run_without_profiling()
