# utils/profiling_utils.py
import os
import time
import psutil
import functools
from typing import Dict, Any, Optional
from viztracer import VizTracer
from memory_profiler import profile
import logging
from datetime import datetime

class PerformanceProfiler:
    """Comprehensive performance profiler for AI Financial Analyzer"""
    
    def __init__(self, output_dir: str = "profiling_results"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.process = psutil.Process()
        self.start_memory = None
        self.start_time = None
        
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage in MB"""
        memory_info = self.process.memory_info()
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,  # Resident Set Size
            "vms_mb": memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            "percent": self.process.memory_percent()
        }
    
    def get_cpu_usage(self) -> float:
        """Get current CPU usage percentage"""
        return self.process.cpu_percent()
    
    def start_monitoring(self):
        """Start performance monitoring"""
        self.start_time = time.time()
        self.start_memory = self.get_memory_usage()
        logging.info(f"Performance monitoring started - Initial memory: {self.start_memory['rss_mb']:.2f} MB")
    
    def stop_monitoring(self, operation_name: str) -> Dict[str, Any]:
        """Stop monitoring and return performance metrics"""
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        duration = end_time - self.start_time if self.start_time else 0
        memory_delta = end_memory['rss_mb'] - self.start_memory['rss_mb'] if self.start_memory else 0
        
        metrics = {
            "operation": operation_name,
            "duration_seconds": round(duration, 3),
            "memory_start_mb": round(self.start_memory['rss_mb'], 2) if self.start_memory else 0,
            "memory_end_mb": round(end_memory['rss_mb'], 2),
            "memory_delta_mb": round(memory_delta, 2),
            "cpu_percent": self.get_cpu_usage(),
            "timestamp": datetime.now().isoformat()
        }
        
        logging.info(f"Performance metrics for {operation_name}: {metrics}")
        return metrics

def profile_function(operation_name: str, save_trace: bool = True):
    """Decorator to profile individual functions with VizTracer and memory monitoring"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            profiler = PerformanceProfiler()
            profiler.start_monitoring()
            
            if save_trace:
                # Create unique filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                trace_file = f"profiling_results/{operation_name}_{timestamp}.json"
                
                tracer = VizTracer(
                    output_file=trace_file,
                    max_stack_depth=50,
                    include_files=["*.py"],
                    exclude_files=["*/site-packages/*"]
                )
                
                tracer.start()
                try:
                    result = func(*args, **kwargs)
                finally:
                    tracer.stop()
                    tracer.save()
                    logging.info(f"VizTracer trace saved to: {trace_file}")
            else:
                result = func(*args, **kwargs)
            
            metrics = profiler.stop_monitoring(operation_name)
            
            # Save metrics to file
            metrics_file = f"profiling_results/{operation_name}_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            import json
            with open(metrics_file, 'w') as f:
                json.dump(metrics, f, indent=2)
            
            return result
        return wrapper
    return decorator

def profile_api_endpoint(endpoint_name: str):
    """Decorator specifically for API endpoints"""
    return profile_function(f"api_{endpoint_name}", save_trace=True)

def profile_ocr_operation(ocr_engine: str):
    """Decorator for OCR operations"""
    return profile_function(f"ocr_{ocr_engine}", save_trace=True)

def profile_rag_operation(operation_type: str):
    """Decorator for RAG operations (embedding, retrieval, etc.)"""
    return profile_function(f"rag_{operation_type}", save_trace=True)

class MemoryTracker:
    """Context manager for tracking memory usage during operations"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.profiler = PerformanceProfiler()
    
    def __enter__(self):
        self.profiler.start_monitoring()
        return self.profiler
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        metrics = self.profiler.stop_monitoring(self.operation_name)
        return False  # Don't suppress exceptions

# Memory profiling decorator using memory_profiler
def memory_profile(func):
    """Decorator for detailed line-by-line memory profiling"""
    return profile(func)
