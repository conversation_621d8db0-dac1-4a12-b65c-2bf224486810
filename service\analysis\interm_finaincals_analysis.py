import os
import json
import traceback
import logging
import time
from utils.file_utils import setup_logging, retreive_files_from_google_drive, get_data_dir
from dotenv import load_dotenv
from service.analysis.analysis_interface import Analysis
from document_processor import DocumentProcessor
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams 
from langchain_qdrant import QdrantVectorStore
from langchain_core.output_parsers import JsonOutputParser
from validation.schema import InterimFinancialAnalysis
from pydantic import BaseModel
from dto.interim_financial_request import InterimFinancialRequest
from langchain_core.prompts import PromptTemplate
from langchain.chains import RetrievalQA






setup_logging()
load_dotenv()

class IntermFinancialAnalysis(Analysis):
    def perform_analysis(self, req: BaseModel, analysis_id: str) -> None:
        
        output_dir=os.path.join("outputs",analysis_id)
        meta_path=os.path.join(output_dir,"job_meta.json")
        result_path=os.path.join(output_dir,"ratios_result.json")
        start_time=time.time()

        params =req
        interimFinancial = params.interimFinancial
        durationInMonths = params.durationInMonths
        audited = params.audited
        annualization = params.annualization
        
        data_dir = ""
        files = []
        if (req.dirUrl != "") :
            data_dir = get_data_dir(req.dirUrl)
            files = retreive_files_from_google_drive(data_dir)

        try:
            os.makedirs(output_dir, exist_ok=True)
            with open(meta_path,"w") as f:
                json.dump({"status":"processing"},f)
            # Step 1: Process documents and split into chunks
            processor=DocumentProcessor(
                data_dir=data_dir,
                output_dir=output_dir,
                ocr_engine=os.getenv("OCR_ENGINE", "tesseract"),
                abbyy_app_id=os.getenv("ABBYY_APP_ID"),
                abbyy_password=os.getenv("ABBYY_PASSWORD")
            )

            all_chunks=[]
            for filename in files:
                try:
                    if (not req.dirUrl):
                        processor.load_document_from_base64(filename)
                    else:
                        docs=processor.load_document_from_url(filename)
                    if not docs:
                        logging.warning(f"No content extracted from {filename}")
                        continue
                    logging.info("start chunking document")
                    chunks = processor.split_document(docs)
                    if not chunks:
                        logging.warning(f"No chunks extracted from {filename}")
                        continue
                    logging.info("finished chunking document!")
                    all_chunks.extend(chunks)
                    processor.save_chunks_to_jsonl(chunks, filename)
                except Exception as file_err:
                    logging.exception(f"Error processing file {filename}: {file_err}")
            if not all_chunks:
                raise ValueError("No valid documents or text chunks found to process.")
            
            # step 2: Vectorization using Qdrant Vectore Store DB

            embedding_model=OpenAIEmbeddings(
                model="text-embedding-3-small",
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )

            try:
                # Use Qdrant for vector storage
                client = QdrantClient(path="outputs/qdrant_data")
                #client= QdrantClient(":memory:")  # Use in-memory Qdrant for testing

                client.create_collection(
                    collection_name=analysis_id,
                    vectors_config=VectorParams(size=1536, distance=Distance.COSINE),
                )
                vectorstore = QdrantVectorStore(
                client=client,
                collection_name=analysis_id,
                embedding=embedding_model,
                        )
                vectorstore.add_documents(all_chunks) 
            except IndexError:
                raise ValueError("Vectorstore creation failed: no embeddings found. Possible OCR or chunking issue.")
            # step 3:Prompt and QA with LLM
            retriever=vectorstore.as_retriever(search_kwargs={"k": 40})

            parser=JsonOutputParser(pydantic_object=InterimFinancialAnalysis)
            prompt = PromptTemplate(
    template="""
                **Persona**:
                You are a **professional Financial Analyst** with expertise in reviewing interim financial reports for early-stage and mid-sized companies. You excel at transforming raw financial data into precise analytical summaries suitable for investor updates and internal reporting.

                **Instruction (Main Task)**:
                Generate a **clear, concise, and neutral paragraph** that analyzes the company’s interim financials, comparing them to the prior period using the provided inputs.

                 **Context (Additional Information)**:
                This analysis is meant to summarize the company’s financial performance based on interim financials submitted through a standardized API. You are given:

                - **Period & Duration** — from `{interimFinancial}` over `{durationInMonths}` months.
                - **Audited** — whether the data is `{audited}`.
                - **Annualization** — whether the figures are `{annualization}`.
                - **Revenue, Gross Profit, Net Income** — each with actual amounts and %% change vs prior period.
                - **Goal**: Interpret performance (improving/stable/declining) and conclude with financial health impact.

                 **Format (Structure)**:
                Use **one paragraph**, structured as follows (replace bracketed placeholders with real values):
                ---
                The company’s interim financials for the period ending [Date], covering [X months], are [audited/unaudited] and [annualized/not annualized]. Revenue for the period reached [Amount], representing a [Y%% increase/decrease] compared to the same period last year. Gross profit was [Amount], showing a [Z%% change], while net income stood at [Amount], reflecting a [W%% change]. Overall, interim performance indicates [positive/steady/weakening] trends, supporting [continued growth / a stable position / the need for closer monitoring].
                ---

                 **Audience**:
                Your summary will be read by **executives, board members, and investors** who need a quick and objective view of company performance without going into detailed financial statements.

                 **Tone**:
                - Maintain a **professional, analytical, and neutral** tone.
                - Do **not** invent or assume numbers.
                - If any input is missing, skip that line — do not fabricate placeholders.

                 **Output Format**:
                Respond **only** in this JSON format and **strictly maintain field order**:
                {format_instructions}

                 **Context**:
                {context}

                 **Question**:
                {question}
""",
    input_variables=["context", "question"],
    partial_variables={
        "format_instructions": parser.get_format_instructions(),
        "interimFinancial": interimFinancial,
        "durationInMonths": durationInMonths,
        "audited": audited,
        "annualization": annualization
    }
)





            llm=ChatOpenAI(model="gpt-4.1-mini", temperature=0)
            qa_chain=RetrievalQA.from_chain_type(
                llm=llm,
                retriever=retriever,
                return_source_documents=False,
                chain_type_kwargs={"prompt": prompt}
            )

            query = (
                        "Based on the provided interim financial data, analyze and summarize the company’s performance in a single paragraph, "
                        "covering: reporting period and months covered, whether the report is audited or annualized, key financial figures "
                        "(Revenue, Gross Profit, Net Income) and their percentage change from the previous period, an overall interpretation "
                        "of performance (improving/stable/declining), and a final one-line conclusion on financial health or funding outlook. "
                        "Use a neutral, factual tone and real values where possible."
                    )

            
            raw_result=qa_chain.invoke({"query":query})
            parsed_result=parser.parse(raw_result["result"])

            with open(result_path, "w") as f:
                json.dump(parsed_result, f, indent=2)

            with open(meta_path, "w") as f:
                json.dump({"status": "done"}, f)

            duration = round(time.time() - start_time, 2)
            logging.info(f"Analysis job {analysis_id} completed in {duration} seconds")

        except Exception as e:
            tb = traceback.format_exc()
            logging.error(f"Job {analysis_id} failed: {tb}")
            with open(meta_path, "w") as f:
                json.dump({"status": "failed","reason": str(e),}, f)






            

            


        
