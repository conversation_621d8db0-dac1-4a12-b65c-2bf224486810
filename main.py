import os
from document_processor import DocumentProcessor
from utils.file_utils import setup_logging
import logging
setup_logging()
from dotenv import load_dotenv
load_dotenv()
import json
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings

logging.info(f"Loaded ABBYY_APP_ID from .env: {os.getenv('ABBYY_APP_ID')}")
logging.info(f"Loaded ABBYY_PASSWORD from .env: {os.getenv('ABBYY_PASSWORD')}")

ocr_engine = "tesseract" 
#ocr_engine = "abbyy"   

OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
embedding_model = OpenAIEmbeddings(
        model="text-embedding-3-small",
        openai_api_key=OPENAI_API_KEY
        )


def process_documents():
    data_dir = "data"
    output_dir = "outputs"
    processor = DocumentProcessor(
    data_dir=data_dir,
    output_dir=output_dir,
    ocr_engine=ocr_engine,
    abbyy_app_id=os.getenv('ABBYY_APP_ID'),
    abbyy_password=os.getenv('ABBYY_PASSWORD')
    )


    all_chunks = []
    for filename in os.listdir(data_dir):
        try:
                logging.info(f"Processing file: {filename}")
                docs = processor.load_document(filename)
                chunks = processor.split_document(docs)
                all_chunks.extend(chunks)
                
                processor.save_chunks_to_jsonl(chunks, filename)
                logging.info(f"Finished processing {filename} with {len(chunks)} chunks.")
        except Exception as e:
                error_msg = f"Error processing {filename}: {e}"
                logging.error(error_msg,exc_info=True)
    logging.info(f"Total chunks created: {len(all_chunks)}")
    logging.info("Processing complete. Check outputs and error log if needed.")


    
    
    vectorstore = FAISS.from_documents(all_chunks, embedding_model)
    faiss_index_path = os.path.join(output_dir, "faiss_index")
    vectorstore.save_local(faiss_index_path)
    logging.info(f"Vector store saved to: {faiss_index_path}")




if __name__ == "__main__":
    process_documents()