# Use official Python 3.12.9 slim image as base
FROM python:3.12.9-slim

# Set working directory
WORKDIR /app

# Copy requirements file
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy .env file and application code
COPY .env .
COPY . .

# Expose port for Flask (default 8000)
EXPOSE 8000

# Set environment variable to load .env file
ENV PYTHONUNBUFFERED=1

# Command to run the Flask application
CMD ["python", "run.py"]