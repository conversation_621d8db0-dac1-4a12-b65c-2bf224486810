# Use official Python 3.12.9 slim image as base
FROM python:3.12.9-slim

ARG LANGSMITH_TRACING
ARG LANGSMITH_PROJECT
ARG LANGSMITH_ENDPOINT
ARG OCR_ENGINE
ARG OPENAI_API_KEY
ARG LANGSMITH_API_KEY
ARG ABBYY_APP_ID
ARG ABBYY_PASSWORD

ENV LANGSMITH_TRACING=$LANGSMITH_TRACING
ENV LANGSMITH_PROJECT=$LANGSMITH_PROJECT
ENV LANGSMITH_ENDPOINT=$LANGSMITH_ENDPOINT
ENV OCR_ENGINE=$OCR_ENGINE
ENV OPENAI_API_KEY=$OPENAI_API_KEY
ENV LANGSMITH_API_KEY=$LANGSMITH_API_KEY
ENV ABBYY_APP_ID=$ABBYY_APP_ID
ENV ABBYY_PASSWORD=$ABBYY_PASSWORD

# Set working directory
WORKDIR /app

# Install system dependencies for OCR and document processing
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    tesseract-ocr-eng \
    tesseract-ocr-ara \
    libtesseract-dev \
    poppler-utils \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

COPY . .

# Create necessary directories
RUN mkdir -p /app/outputs /app/data_company_documents

# Expose port for FastAPI (default 8000)
EXPOSE 8000

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Command to run the FastAPI application
CMD ["uvicorn", "routers.routes:app", "--host", "0.0.0.0", "--port", "8000"]