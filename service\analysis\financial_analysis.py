import os
import json
import traceback
import logging
import time
from document_processor import DocumentProcessor
from dotenv import load_dotenv
from pydantic import BaseModel
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams 
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain.chains import RetrievalQA
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from validation.schema import RatioAnalysis
from dto.financial_request import FinancialRequest
from utils.file_utils import setup_logging, retreive_files_from_google_drive, get_data_dir
from service.analysis.analysis_interface import Analysis

setup_logging()
load_dotenv()

class FinancialAnalysis(Analysis):
    def perform_analysis(self, params, req: BaseModel, analysis_id: str) -> None:
        output_dir = os.path.join("outputs", analysis_id)
        meta_path = os.path.join(output_dir, "job_meta.json")
        result_path = os.path.join(output_dir, "ratios_result.json")
        start_time = time.time()

        params = req
        
        data_dir = ""
        files = []
        if (params.dirUrl != "") :
            data_dir = get_data_dir(params.dirUrl)
            files = retreive_files_from_google_drive(data_dir)

        try:
            os.makedirs(output_dir, exist_ok=True)
            with open(meta_path, "w") as f:
                json.dump({"status": "processing"}, f)

            # Step 1: Process documents and split into chunks
            processor = DocumentProcessor(
                data_dir=dir,
                output_dir=output_dir,
                ocr_engine=os.getenv("OCR_ENGINE", "tesseract"),
                abbyy_app_id=os.getenv("ABBYY_APP_ID"),
                abbyy_password=os.getenv("ABBYY_PASSWORD")
            )

            all_chunks = []
            for filename in files:
                try:
                    if (not dir):
                        # the dir is empty
                        processor.load_document_from_base64(filename)
                    else :
                        # the dir is not empty
                        docs = processor.load_document_from_url(filename)
                    if not docs:
                        logging.warning(f"No content extracted from {filename}")
                        continue
                    logging.info("start chunking document")
                    chunks = processor.split_document(docs)
                    if not chunks:
                        logging.warning(f"No chunks extracted from {filename}")
                        continue
                    logging.info("finished chunking document!")
                    all_chunks.extend(chunks)
                    processor.save_chunks_to_jsonl(chunks, filename)
                except Exception as file_err:
                    logging.exception(f"Error processing file {filename}: {file_err}")

            if not all_chunks:
                raise ValueError("No valid documents or text chunks found to process.")

            # Step 2: Vectorization using FAISS
            embedding_model = OpenAIEmbeddings(
                model="text-embedding-3-small",
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )

            try:
                # Use Qdrant for vector storage
                client = QdrantClient(path="outputs/qdrant_data")
                #client= QdrantClient(":memory:")  # Use in-memory Qdrant for testing

                client.create_collection(
                    collection_name=analysis_id,
                    vectors_config=VectorParams(size=1536, distance=Distance.COSINE),
                )
                vectorstore = QdrantVectorStore(
                client=client,
                collection_name=analysis_id,
                embedding=embedding_model,
                        )
                vectorstore.add_documents(all_chunks) 
            except IndexError:
                raise ValueError("Vectorstore creation failed: no embeddings found. Possible OCR or chunking issue.")


            # Step 3: Prompt and QA with LLM
            retriever = vectorstore.as_retriever(search_kwargs={"k": 40})
            #retriever.search_kwargs['k'] = 40

            parser = JsonOutputParser(pydantic_object=RatioAnalysis)
            prompt = PromptTemplate(
                template="""You are a Financial Statement Analyzer.
    Your task is to analyze provided financial data for multiple years. For each year available:

    1. Clearly extract necessary financial figures.
    2. Calculate and interpret each financial ratio:
        - Current Ratio = Current Assets / Current Liabilities
        - Quick Ratio = (Current Assets - Inventory) / Current Liabilities
        - Debt to Equity Ratio = Total Debt / Total Equity
        - Net Profit Margin = Net Profit / Revenue

    For **each year**, provide:
    - The calculated value.
    - The calculation formula explicitly with values.
    - A concise interpretation comment about the financial condition indicated by the ratio for that year.

    Provide your response strictly in the following JSON format:

    {format_instructions}

    Context:
    {context}

    Question:
    {question}
    """,
                input_variables=["context", "question"],
                partial_variables={"format_instructions": parser.get_format_instructions()}
            )

            llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0)
            qa_chain = RetrievalQA.from_chain_type(
                llm=llm,
                retriever=retriever,
                return_source_documents=False,
                chain_type_kwargs={"prompt": prompt}
            )

            query = (
                "Based on the provided multi-year financial data, extract all necessary values to calculate and interpret the following financial ratios for each available year:\n"
                "- Current Ratio = Current Assets / Current Liabilities\n"
                "- Quick Ratio = (Current Assets - Inventory) / Current Liabilities\n"
                "- Debt to Equity Ratio = Total Debt / Total Equity\n"
                "- Net Profit Margin = Net Profit / Revenue\n\n"
                "Ensure you retrieve and use the following fields explicitly where available: "
                "Current Assets, Current Liabilities, Inventory, Total Debt, Total Equity, Net Profit, and Revenue.\n\n"
                "Present the results year by year. For each ratio and year, include:\n"
                "1. The substituted formula with real numbers\n"
                "2. The computed value (rounded to two decimal places)\n"
                "3. A short interpretation of what the ratio indicates about financial health."
            )

            raw_result = qa_chain.invoke({"query": query})
            parsed_result = parser.parse(raw_result["result"])

            with open(result_path, "w") as f:
                json.dump(parsed_result, f, indent=2)

            with open(meta_path, "w") as f:
                json.dump({"status": "done"}, f)

            duration = round(time.time() - start_time, 2)
            logging.info(f"Analysis job {analysis_id} completed in {duration} seconds")

        except Exception as e:
            tb = traceback.format_exc()
            logging.error(f"Job {analysis_id} failed: {tb}")
            with open(meta_path, "w") as f:
                json.dump({
                    "status": "failed",
                    "reason": str(e),
                }, f)
