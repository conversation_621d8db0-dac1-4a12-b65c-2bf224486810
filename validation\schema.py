from pydantic import BaseModel,Field,confloat
from typing import Dict, Optional

# 5 total digits, 1 decimal: max is 9999.9, min is -9999.9
AmountType = confloat(ge=-9999.9, le=9999.9)
class FinancialValue(BaseModel):
    amount:Optional[AmountType]=Field(None, description="The monetary value in the current period, e.g., revenue amount.")
    change:Optional[AmountType]=Field(None, description="The change in the value compared to a previous period, expressed as a percentage or absolute value.")
class InterimFinancialAnalysis(BaseModel):
    revenue:FinancialValue= Field(..., description="Revenue for the period. Includes both the amount and the percent change.")
    grossProfit:FinancialValue= Field(..., description="Gross profit for the period. Includes both the amount and the percent change.")
    netIncome:FinancialValue= Field(..., description="Net income for the period. Includes both the amount and the percent change")
    interimFinancialsAnalysisSummary:str= Field(..., description="Summary of the interim financial analysis.")
    
class EntityBackgroundAnalysiss(BaseModel):
    entityBackgroundAnalysisSummary: str

class RatioYearDetail(BaseModel):
    value: Optional[float]
    formula: Optional[str]
    comment: Optional[str]

class RatioAnalysis(BaseModel):
    current_ratio: Dict[str, RatioYearDetail]
    quick_ratio: Dict[str, RatioYearDetail]
    debt_to_equity: Dict[str, RatioYearDetail]
    net_profit_margin: Dict[str, RatioYearDetail]
    summary: Optional[str]