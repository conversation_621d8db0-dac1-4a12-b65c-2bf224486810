from pydantic import BaseModel
from typing import Dict, Optional

class RatioYearDetail(BaseModel):
    value: Optional[float]
    formula: Optional[str]
    comment: Optional[str]

class RatioAnalysis(BaseModel):
    current_ratio: Dict[str, RatioYearDetail]
    quick_ratio: Dict[str, RatioYearDetail]
    debt_to_equity: Dict[str, RatioYearDetail]
    net_profit_margin: Dict[str, RatioYearDetail]
    summary: Optional[str]
