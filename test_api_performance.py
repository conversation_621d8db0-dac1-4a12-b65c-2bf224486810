# test_api_performance.py
"""
Script to test API performance and generate profiling data
"""
import requests
import time
import json
import os
from datetime import datetime
from utils.profiling_utils import PerformanceProfiler
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class APIPerformanceTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.profiler = PerformanceProfiler()
        self.results = []
    
    def test_endpoint(self, method, endpoint, data=None, description=""):
        """Test a single API endpoint and measure performance"""
        url = f"{self.base_url}{endpoint}"
        
        logger.info(f"Testing {method} {endpoint} - {description}")
        
        # Start profiling
        self.profiler.start_monitoring()
        start_time = time.time()
        
        try:
            if method.upper() == "GET":
                response = requests.get(url)
            elif method.upper() == "POST":
                response = requests.post(url, json=data)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            end_time = time.time()
            
            # Stop profiling
            metrics = self.profiler.stop_monitoring(f"{method}_{endpoint.replace('/', '_')}")
            
            # Add response info to metrics
            metrics.update({
                "endpoint": endpoint,
                "method": method,
                "description": description,
                "status_code": response.status_code,
                "response_time_ms": round((end_time - start_time) * 1000, 2),
                "response_size_bytes": len(response.content),
                "success": response.status_code < 400
            })
            
            self.results.append(metrics)
            
            logger.info(f"✅ {description} - {response.status_code} - {metrics['response_time_ms']}ms - {metrics['memory_delta_mb']}MB")
            
            return response
            
        except Exception as e:
            logger.error(f"❌ {description} - Error: {str(e)}")
            return None
    
    def run_comprehensive_test(self, test_data_dir="data_company_documents/test_company"):
        """Run comprehensive API performance tests"""
        logger.info("🚀 Starting comprehensive API performance test...")
        
        # Test data - replace with your actual test data
        test_payload = {
            "dir_url": test_data_dir
        }
        
        # 1. Test Validate API
        self.test_endpoint(
            "POST", 
            "/api/ai/validate-documents", 
            test_payload,
            "Document Validation"
        )
        
        # 2. Test Analyse API
        self.test_endpoint(
            "POST", 
            "/api/ai/analyse", 
            test_payload,
            "Document Analysis & Embedding"
        )
        
        # 3. Test Results API
        company_id = os.path.basename(test_data_dir)
        self.test_endpoint(
            "GET", 
            f"/api/ai/get-results/{company_id}", 
            None,
            "Financial Analysis Results"
        )
        
        # 4. Test MVP API (full pipeline)
        self.test_endpoint(
            "POST", 
            "/api/ai/mvp_analyse", 
            test_payload,
            "MVP Full Pipeline Analysis"
        )
        
        # Save results
        self.save_results()
        self.print_summary()
    
    def save_results(self):
        """Save performance test results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"profiling_results/api_performance_test_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        logger.info(f"📊 Performance results saved to: {results_file}")
    
    def print_summary(self):
        """Print performance test summary"""
        print("\n" + "="*80)
        print("📊 API PERFORMANCE TEST SUMMARY")
        print("="*80)
        
        total_time = sum(r['response_time_ms'] for r in self.results)
        total_memory = sum(r['memory_delta_mb'] for r in self.results)
        
        print(f"Total Tests: {len(self.results)}")
        print(f"Total Time: {total_time:.2f}ms")
        print(f"Total Memory Delta: {total_memory:.2f}MB")
        print(f"Average Response Time: {total_time/len(self.results):.2f}ms")
        print(f"Average Memory Usage: {total_memory/len(self.results):.2f}MB")
        
        print("\n📋 Individual Test Results:")
        print("-" * 80)
        
        for result in self.results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['description']:<30} "
                  f"{result['response_time_ms']:>8.2f}ms "
                  f"{result['memory_delta_mb']:>8.2f}MB "
                  f"{result['status_code']:>4}")
        
        print("\n🔍 Detailed Metrics:")
        print("-" * 80)
        
        for result in self.results:
            print(f"\n{result['description']}:")
            print(f"  Response Time: {result['response_time_ms']}ms")
            print(f"  Memory Start: {result['memory_start_mb']}MB")
            print(f"  Memory End: {result['memory_end_mb']}MB")
            print(f"  Memory Delta: {result['memory_delta_mb']}MB")
            print(f"  CPU Usage: {result['cpu_percent']}%")
            print(f"  Response Size: {result['response_size_bytes']} bytes")

def main():
    """Main function to run API performance tests"""
    # Ensure profiling directory exists
    os.makedirs("profiling_results", exist_ok=True)
    
    # Create tester instance
    tester = APIPerformanceTester()
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/docs")
        if response.status_code == 200:
            logger.info("✅ Server is running, starting performance tests...")
            tester.run_comprehensive_test()
        else:
            logger.error("❌ Server is not responding properly")
    except requests.exceptions.ConnectionError:
        logger.error("❌ Cannot connect to server. Make sure the API is running on http://localhost:8000")
        print("\n💡 To start the server:")
        print("   python run_profiled.py --profile")

if __name__ == "__main__":
    main()
