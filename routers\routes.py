# routers/routes.py
from fastapi import FastAPI
from routers.validate_api import router as validate_router
from routers.analyse_api import router as analyse_router
from routers.results_api import router as results_router
from routers.mvp import router as mvp_router

app = FastAPI()

app.include_router(validate_router)
app.include_router(analyse_router)
app.include_router(results_router)
app.include_router(mvp_router)
