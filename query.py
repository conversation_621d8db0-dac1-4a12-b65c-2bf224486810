import os
from dotenv import load_dotenv
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings,ChatOpenAI
from langchain.chains import RetrievalQA
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser

# Load API keys from .env
load_dotenv()
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

# Load vector store from disk
embedding_model = OpenAIEmbeddings(
        model="text-embedding-3-small",
        openai_api_key=OPENAI_API_KEY
        )
vectorstore = FAISS.load_local("outputs/faiss_index", embedding_model,allow_dangerous_deserialization=True)

# Create retriever
retriever = vectorstore.as_retriever()




# Define a custom prompt template
custom_prompt = PromptTemplate.from_template(
 """You are a Financial Statement Analyzer from companyinsight.ai.
                        Analyze the provided financial context step-by-step to extract financial insights.

                        Specifically:
                        - Focus on Ratio-based analysis (e.g., DSCR, D/E, Profit <PERSON>gin, Current Ratio, etc.)
                        - Perform Trend-based analysis (e.g., revenue, profit, or cash flow over time)


                                        Context:
                                        {context}

                                        Question:
                                        {question}

                        
                        """
)



# Create LLM
llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0)

# Create QA chain
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    retriever=retriever,
    return_source_documents=True,
    chain_type_kwargs={"prompt": custom_prompt}
)

# Ask a question

query = "Based on the financial statements, provide ratio-based and trend-based financial analysis."

# Invoke the chain with the query
result = qa_chain.invoke({"query": query})


# Print result
print("\nAnswer:")
print(result["result"])







# print("\nSources:")
# for doc in result["source_documents"]:
#     print(f"- {doc.metadata}")
