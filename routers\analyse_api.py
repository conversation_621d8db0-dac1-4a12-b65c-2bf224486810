# routers/analyse_api.py
from fastapi import APIRouter, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import os
from document_processor import DocumentProcessor
from dotenv import load_dotenv
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings
from utils.gdrive_utils import download_gdrive_folder_to_data_dir
import time
import logging
from utils.file_utils import setup_logging
import traceback


setup_logging()
load_dotenv()
router = APIRouter()

class AnalyseRequest(BaseModel):
    dir_url: str

@router.post("/api/ai/analyse")
def analyse_documents(req: AnalyseRequest):
    start_time = time.time() 
    try:
        # Always use Google Drive utility (works for both GDrive and local URLs if you want)
        data_dir = download_gdrive_folder_to_data_dir(
            req.dir_url,
            base_dir="data_company_documents",
            service_account_file="service_accounts/gdrive_readonly_key.json"
        )
        company_id = os.path.basename(data_dir)
        output_dir = os.path.join("outputs", company_id)
        faiss_index_path = os.path.join(output_dir, "faiss_index")

        os.makedirs(output_dir, exist_ok=True)

        processor = DocumentProcessor(
            data_dir=data_dir,
            output_dir=output_dir,
            ocr_engine=os.getenv("OCR_ENGINE", "tesseract"),
            abbyy_app_id=os.getenv("ABBYY_APP_ID"),
            abbyy_password=os.getenv("ABBYY_PASSWORD")
        )

        all_chunks = []
        for filename in os.listdir(data_dir):
            try:
                docs = processor.load_document(filename)
                chunks = processor.split_document(docs)
                if not chunks:
                    raise ValueError(f"No chunks generated from {filename}")
                all_chunks.extend(chunks)
                processor.save_chunks_to_jsonl(chunks, filename)
            except Exception as e:
                tb = traceback.format_exc()
                logging.error(f"[ERROR] Failed to process {filename}: {str(e)}")
                logging.error(f"[TRACEBACK]\n{tb}")
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={
                        "status": "error",
                        "reason": f"Chunking failed for file: {filename}",
                        "trace": tb
                    }
                )

        embedding_model = OpenAIEmbeddings(
            model="text-embedding-3-small",
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )
        vectorstore = FAISS.from_documents(all_chunks, embedding_model)
        vectorstore.save_local(faiss_index_path)
        end_time = time.time()
        duration = round(end_time - start_time, 2)
        logging.info(f"Time taken: {duration} seconds")

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": "success",
                "message": f"Processed and indexed {len(all_chunks)} chunks",
                "chunks_count": len(all_chunks),
                "faiss_index_path": faiss_index_path
            }
        )

    except Exception as e:
        tb = traceback.format_exc()
        logging.error(f"[ERROR] analyse_documents failed: {str(e)}\n{tb}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"status": "error", "reason": str(e), "trace": tb}
        )

