[{"endpoint": "/api/ai/validate-documents", "method": "POST", "description": "Document Validation", "status_code": 500, "duration_seconds": 2.077, "success": false, "timestamp": "2025-06-15T12:55:23.590613"}, {"endpoint": "/api/ai/analyse", "method": "POST", "description": "Document Analysis (OCR + RAG)", "status_code": 500, "duration_seconds": 2.093, "success": false, "timestamp": "2025-06-15T12:55:25.683917"}, {"endpoint": "/api/ai/get-results/test_company", "method": "GET", "description": "Financial Analysis Results", "status_code": 500, "duration_seconds": 2.061, "success": false, "timestamp": "2025-06-15T12:55:27.744854"}]