# ocr_engine.py
import fitz
from PIL import Image
import io
import pytesseract
#pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
import re
import os
from langchain_core.documents import Document
from ABBYY import CloudOCR
import logging
from utils.file_utils import setup_logging
from utils.pyinstrument_profiler import profile_ocr, quick_profile
setup_logging()

class OCREngine:
    def __init__(self, engine="tesseract", abbyy_app_id=None, abbyy_password=None):
        self.engine = engine
        self.abbyy_app_id = abbyy_app_id
        self.abbyy_password = abbyy_password

        if self.engine == "abbyy":
            if not self.abbyy_app_id or not self.abbyy_password:
                raise ValueError("ABBYY credentials missing")
            self.abbyy_client = CloudOCR(
                application_id=self.abbyy_app_id,
                password=self.abbyy_password
            )

    def clean_text(self, text: str) -> str:
        """Normalize whitespace and control characters."""
        text = re.sub(r'\s+', ' ', text)
        #text = re.sub(r'[^\x20-\x7E]+', ' ', text)
        return text.strip()

    def ocr_pdf(self, file_path: str):
        """Route to selected OCR engine."""
        if self.engine == "tesseract":
            logging.info(f"Using Tesseract OCR for {file_path}")
            return self._ocr_with_tesseract(file_path)
        elif self.engine == "abbyy":
            logging.info(f"Using ABBYY OCR for {file_path}")
            return self._ocr_with_abbyy(file_path)
        else:
            raise ValueError(f"Unsupported OCR engine: {self.engine}")

    @profile_ocr("tesseract")
    def _ocr_with_tesseract(self, file_path: str):
        doc = fitz.open(file_path)
        pages = []

        for i, page in enumerate(doc):
            with quick_profile(f"tesseract_page_{i+1}"):
                pix = page.get_pixmap(dpi=300)
                img = Image.open(io.BytesIO(pix.tobytes("png")))
                text = pytesseract.image_to_string(img, lang="eng")
                cleaned = self.clean_text(text)
                pages.append(Document(
                    page_content=cleaned,
                    metadata={"source": file_path, "page": i + 1, "ocr": "tesseract"}
                ))

        return pages

    @profile_ocr("abbyy")
    def _ocr_with_abbyy(self, file_path: str):
        with quick_profile("abbyy_processing"):
            with open(file_path, 'rb') as pdf:
                files = {os.path.basename(file_path): pdf}
                result = self.abbyy_client.process_and_download(
                    files,
                    exportFormat='txt',
                    language='English',
                )

            txt_stream = result.get('txt')
            text = txt_stream.read().decode('utf-8')
            cleaned = self.clean_text(text)

            return [Document(
                page_content=cleaned,
                metadata={"source": file_path, "ocr": "abbyy"}
            )]
