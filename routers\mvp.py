# mvp.py
from fastapi import APIRouter, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from document_processor import DocumentProcessor
from dotenv import load_dotenv
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain.chains import RetrievalQA
from langchain_core.prompts import Prompt<PERSON><PERSON>plate
from langchain_core.output_parsers import JsonOutputParser
from validation.schema import RatioAnalysis
from utils.gdrive_utils import download_gdrive_folder_to_data_dir
from utils.profiling_utils import profile_api_endpoint, MemoryTracker
import os
import time
import logging
from utils.file_utils import setup_logging


setup_logging()
load_dotenv()
router = APIRouter()

class AnalyseAndRespondRequest(BaseModel):
    dir_url: str

@router.post("/api/ai/mvp_analyse")
@profile_api_endpoint("mvp_analyse")
def mvp_analyse(req: AnalyseAndRespondRequest):
    start_time = time.time()
    try:
        # Step 1: Download data
        data_dir = download_gdrive_folder_to_data_dir(
            req.dir_url,
            base_dir="data_company_documents",
            service_account_file="service_accounts/gdrive_readonly_key.json"
        )
        company_id = os.path.basename(data_dir)
        output_dir = os.path.join("outputs", company_id)
        faiss_index_path = os.path.join(output_dir, "faiss_index")
        os.makedirs(output_dir, exist_ok=True)

        # Step 2: Process documents
        processor = DocumentProcessor(
            data_dir=data_dir,
            output_dir=output_dir,
            ocr_engine=os.getenv("OCR_ENGINE", "tesseract"),
            abbyy_app_id=os.getenv("ABBYY_APP_ID"),
            abbyy_password=os.getenv("ABBYY_PASSWORD")
        )
        all_chunks = []
        for filename in os.listdir(data_dir):
            try:
                docs = processor.load_document(filename)
                chunks = processor.split_document(docs)
                all_chunks.extend(chunks)
                processor.save_chunks_to_jsonl(chunks, filename)
            except Exception:
                continue

        if not all_chunks:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"status": "error", "reason": "No valid chunks generated"}
            )

        # Step 3: Build vector store
        embedding_model = OpenAIEmbeddings(
            model="text-embedding-3-small",
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )
        vectorstore = FAISS.from_documents(all_chunks, embedding_model)
        vectorstore.save_local(faiss_index_path)

        # Step 4: LLM Question Answering
        retriever = vectorstore.as_retriever()
        retriever.search_kwargs['k'] = 40

        parser = JsonOutputParser(pydantic_object=RatioAnalysis)
        prompt = PromptTemplate(
            template="""
You are a Financial Statement Analyzer.

Your task is to analyze provided financial data for multiple years. For each year available:

1. Clearly extract necessary financial figures.
2. Calculate and interpret each financial ratio:
    - Current Ratio = Current Assets / Current Liabilities
    - Quick Ratio = (Current Assets - Inventory) / Current Liabilities
    - Debt to Equity Ratio = Total Debt / Total Equity
    - Net Profit Margin = Net Profit / Revenue

For **each year**, provide:
- The calculated value.
- The calculation formula explicitly with values.
- A concise interpretation comment about the financial condition indicated by the ratio for that year.

Provide your response strictly in the following JSON format:

{format_instructions}

Context:
{context}

Question:
{question}
""",
            input_variables=["context", "question"],
            partial_variables={"format_instructions": parser.get_format_instructions()}
        )

        llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0)
        qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            retriever=retriever,
            return_source_documents=False,
            chain_type_kwargs={"prompt": prompt}
        )

        query = (
            "Based on the provided multi-year financial data, extract all necessary values to calculate and interpret the following financial ratios for each available year:\n"
            "- Current Ratio = Current Assets / Current Liabilities\n"
            "- Quick Ratio = (Current Assets - Inventory) / Current Liabilities\n"
            "- Debt to Equity Ratio = Total Debt / Total Equity\n"
            "- Net Profit Margin = Net Profit / Revenue\n\n"
            "Ensure you retrieve and use the following fields explicitly where available: "
            "Current Assets, Current Liabilities, Inventory, Total Debt, Total Equity, Net Profit, and Revenue.\n\n"
            "Present the results year by year. For each ratio and year, include:\n"
            "1. The substituted formula with real numbers\n"
            "2. The computed value (rounded to two decimal places)\n"
            "3. A short interpretation of what the ratio indicates about financial health."
        )

        raw_result = qa_chain.invoke({"query": query})
        parsed_result = parser.parse(raw_result["result"])
        end_time = time.time()
        duration = round(end_time - start_time, 2)
        logging.info(f"Time taken: {duration} seconds")
        

        return JSONResponse(
            status_code=status.HTTP_200_OK, content= parsed_result
                    )

    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"status": "error", "reason": str(e)}
        )
