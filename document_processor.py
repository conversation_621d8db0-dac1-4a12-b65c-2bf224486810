import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="openpyxl")
import os
import re
from langchain_community.document_loaders import UnstructuredExcelLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from ocr_engine import OCREngine
from utils.profiling_utils import profile_function, MemoryTracker
import json




class DocumentProcessor:

    def __init__(self, data_dir: str, output_dir: str,ocr_engine="tesseract", abbyy_app_id=None, abbyy_password=None):
        self.data_dir = data_dir
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.ocr_engine = OCREngine(engine=ocr_engine, abbyy_app_id=abbyy_app_id, abbyy_password=abbyy_password)



    def get_file_extension(self, filename: str):
        return os.path.splitext(filename)[-1].lower()

    def get_loader(self, file_path: str):
        ext = self.get_file_extension(file_path)
        if ext == '.xlsx':
            return UnstructuredExcelLoader(file_path, mode='elements')
        else:
            raise ValueError(f"Unsupported non-PDF file type: {ext}")

    def clean_text(self, text: str):
        text = re.sub(r'\s+', ' ', text)
        #text = re.sub(r'[^\x20-\x7E]+', ' ', text)
        return text.strip()

    def load_document(self, filename: str):
        with MemoryTracker(f"load_document_{filename}"):
            file_path = os.path.join(self.data_dir, filename)
            ext = self.get_file_extension(filename)
            if ext == '.pdf':
                return self.ocr_engine.ocr_pdf(file_path)
            else:
                loader = self.get_loader(file_path)
                return loader.load()

    def split_document(self, documents, chunk_size=1000, overlap=200):
        with MemoryTracker("split_document"):
            splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=overlap,
                length_function=len,
                add_start_index=True
            )
            texts = [doc.page_content for doc in documents]
            metadatas = [doc.metadata for doc in documents]
            return splitter.create_documents(texts, metadatas=metadatas)

















    def save_chunks_to_txt(self, chunks, filename: str):
        base_name = os.path.splitext(filename)[0]
        out_path = os.path.join(self.output_dir, f"{base_name}_chunks.txt")
        with open(out_path, 'w', encoding='utf-8') as f:
            for i, chunk in enumerate(chunks):
                #f.write(f"--- Chunk {i+1} ---\n")
                f.write(chunk.page_content + "\n\n")

    def save_chunks_to_json(self, chunks, filename: str):
        base_name = os.path.splitext(filename)[0]
        out_path = os.path.join(self.output_dir, f"{base_name}_chunks.json")
        chunks_data = [
            {"text": chunk.page_content, "metadata": chunk.metadata}
            for chunk in chunks
        ]
        with open(out_path, 'w', encoding='utf-8') as f:
            json.dump(chunks_data, f, ensure_ascii=False, indent=2)

    def save_chunks_to_jsonl(self, chunks, filename: str):
        base_name = os.path.splitext(filename)[0]
        out_path = os.path.join(self.output_dir, f"{base_name}_chunks.jsonl")

        with open(out_path, "w", encoding="utf-8") as f:
            for chunk in chunks:
                f.write(json.dumps({
                    "text": chunk.page_content,
                    "metadata": chunk.metadata
                }, ensure_ascii=False) + "\n")