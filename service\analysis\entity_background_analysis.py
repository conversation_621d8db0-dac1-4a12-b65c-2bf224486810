import os
import json
import traceback
import logging
import time
from utils.file_utils import setup_logging,retreive_files_from_google_drive,get_data_dir
from dotenv import load_dotenv
from service.analysis.analysis_interface import Analysis
from document_processor import DocumentProcessor
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams 
from langchain_qdrant import QdrantVectorStore
from langchain_core.output_parsers import JsonOutputParser
from validation.schema import EntityBackgroundAnalysiss
from pydantic import BaseModel
from langchain_core.prompts import PromptTemplate
from langchain.chains import RetrievalQA
from langchain_tavily import TavilySearch
from langgraph.prebuilt import create_react_agent
from langchain_core.messages import HumanMessage
import re 

setup_logging()
load_dotenv()

class EntityBackgroundAnalysis(Analysis):
    def perform_analysis(self,req:BaseModel, analysis_id: str = None) -> None:
        output_dir = os.path.join("outputs", analysis_id)
        meta_path = os.path.join(output_dir, "job_meta.json")
        result_path = os.path.join(output_dir, "ratios_result.json")
        start_time = time.time()

        try:
            os.makedirs(output_dir, exist_ok=True)
            with open(meta_path, "w") as f:
                json.dump({"status": "processing"}, f)

            openai_api_key = os.getenv("OPENAI_API_KEY")
            tavily_key = os.getenv("TAVILY_API_KEY")
            llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0, api_key=openai_api_key)

            context_parts = []
            params=req
            url=params.storeUrl
            entity_brief=params.entityBrief
            domain_match=re.search(r"(?:https?://)?(?:[^@/\n]+@)?(?:www\.)?([^:/\n]+)",
                                   url,
                                   flags=re.IGNORECASE|re.MULTILINE)
            extracted_domain=domain_match.group(1) if domain_match else None
            logging.info(f"extracted domain {extracted_domain}")
            # Add website content using Tavily if URL provided
            if url:
                logging.info(f"Fetching website content from {url}")
                tavily_search = TavilySearch(
                    api_key=tavily_key,
                    max_results=7,
                    include_raw_content=True,
                    include_answer=True,
                    search_depth="advanced",
                    include_domains=[extracted_domain]if extracted_domain else [],  # Ensure the domain is relevant
                )
                agent_executor = create_react_agent(llm, [tavily_search])
                query = f"Scrape all business information and summarize from: {url}"
                response = agent_executor.invoke({"messages": [{"role": "user", "content": query}]})
                url_content = response["messages"][-1].content
                context_parts.append(f"Website Content:\n{url_content}")

            # Add entity brief if provided
            if entity_brief:
                logging.info("Using provided entity brief")
                context_parts.append(f"Entity Brief:\n{entity_brief}")

            # Handle document attachment if provided
            data_dir=""
            files=[]
            if(params.dirUrl!=""):
                data_dir=get_data_dir(params.dirUrl)
                files=retreive_files_from_google_drive(data_dir)
            all_chunks = []
            if files and len(files) > 0:
                logging.info(f"Processing {len(files)} document(s)")
                processor = DocumentProcessor(
                    data_dir=data_dir,
                    output_dir=output_dir,
                    ocr_engine=os.getenv("OCR_ENGINE", "tesseract"),
                    abbyy_app_id=os.getenv("ABBYY_APP_ID"),
                    abbyy_password=os.getenv("ABBYY_PASSWORD")
                )
                for filename in files:
                    try:
                        docs = processor.load_document_from_url(filename) if dir else processor.load_document_from_base64(filename)
                        if not docs:
                            logging.warning(f"No content extracted from {filename}")
                            continue
                        chunks = processor.split_document(docs)
                        if not chunks:
                            logging.warning(f"No chunks extracted from {filename}")
                            continue
                        all_chunks.extend(chunks)
                        processor.save_chunks_to_jsonl(chunks, filename)
                    except Exception as file_err:
                        logging.exception(f"Error processing file {filename}: {file_err}")

            # RAG chunk retrieval if there are valid chunks
            rag_result = None
            if all_chunks:
                embedding_model = OpenAIEmbeddings(
                    model="text-embedding-3-small",
                    openai_api_key=openai_api_key
                )
                client = QdrantClient(path="outputs/qdrant_data")
                client.create_collection(
                    collection_name=analysis_id,
                    vectors_config=VectorParams(size=1536, distance=Distance.COSINE),
                )
                vectorstore = QdrantVectorStore(
                    client=client,
                    collection_name=analysis_id,
                    embedding=embedding_model,
                )
                vectorstore.add_documents(all_chunks)
                retriever = vectorstore.as_retriever(search_kwargs={"k": 40})
                qa_chain = RetrievalQA.from_chain_type(
                    llm=llm,
                    retriever=retriever,
                    return_source_documents=False
                )
                rag_result = qa_chain.invoke({
                    "query": "Extract all relevant business, risk, and background information from these documents."
                })["result"]
                context_parts.append(f"Document Content:\n{rag_result}")

            # Compose the full context for the LLM
            combined_context = "\n\n".join(context_parts)

            # Use your validated parser and strict output instructions for JSON output
            parser = JsonOutputParser(pydantic_object=EntityBackgroundAnalysiss)
            prompt = PromptTemplate(
                template="""
You are a professional financial analyst tasked with evaluating the full background of a business entity using all available data sources. 
This data may originate from:
- Uploaded documents (e.g., financial statements, business profiles, licenses),
- Manually entered brief descriptions,
- Website content retrieved from a supplied URL (scraped if needed),
- Or OCR-extracted content from scanned files.

Your evaluation must follow a structured analysis that matches the entity background API requirements, addressing the following dimensions explicitly:

1. Valid Store URL
   Assess whether the URL (if provided) is functional and leads to a credible online presence representing the entity.

2. Entity Brief (Activities, Market, Value Proposition) 
   Clearly describe the company’s core business activities, its target market, and its value proposition. Indicate industries served, technology used, and key differentiators.

3. Supporting Attachments Review
   Evaluate the presence and relevance of attachments such as financial statements, business licenses, certifications (e.g., ISO), employee or capacity data, and CSR or governance reports.

4. Completeness of Data
   Identify any major gaps or missing components, including:
   - Lack of audited financials
   - Missing brief, website, or critical attachments

5. General Consistency
   If both a website and a brief/profile are available, compare them for logical consistency. Check whether the descriptions align in messaging, product claims, or market focus.

6. General Risk Identification
   Note any red flags such as:
   - Ambiguous or vague language
   - Absence of third-party validation
   - Inactive or fake URLs
   - Internal contradictions
   - Lack of digital presence

7. Final Summary Verdict
   Provide a single-paragraph, neutral, and factual summary assessing the entity’s operational viability, credibility, transparency, and overall risk level. If audited financials are missing, explicitly recommend obtaining them.

Audience:  
Your summary will be consumed by compliance officers, credit analysts, and risk assessors performing due diligence and creditworthiness evaluations.

Tone:  
Maintain a professional, objective, and analytical tone.

Output Format:  
Return the summary as a compact single string without any newline or special characters like \n or \n\n
Respond only using the following JSON schema structure:  
{format_instructions}

Input Data:  
{context}

Query:  
{question}
                """,
                input_variables=["context", "question"],
                partial_variables={"format_instructions": parser.get_format_instructions()}
            )

            query = (
                "Provide a structured entity background analysis addressing all of the following: "
                "1) Clear description of core business activities, target market, and value proposition; "
                "2) Presence, completeness, and relevance of supporting attachments; "
                "3) Completeness of all required information and any significant gaps; "
                "4) (If applicable) Consistency between website and brief/profile; "
                "5) General risks such as lack of clarity, missing data, or non-functional site; "
                "6) End with a single-paragraph, neutral summary verdict on the entity’s operational viability, credibility, and transparency."
            )

            llm=ChatOpenAI(model="gpt-4.1-mini", temperature=0)
            prompt_text = prompt.format(context=combined_context, question=query)
            raw_result = llm.invoke([HumanMessage(content=prompt_text)])
            parsed_result = parser.parse(raw_result.content)

            with open(result_path, "w") as f:
                json.dump(parsed_result, f, indent=2)

            with open(meta_path, "w") as f:
                json.dump({"status": "done"}, f)

            duration = round(time.time() - start_time, 2)
            logging.info(f"Analysis job {analysis_id} completed in {duration} seconds")
            return parsed_result

        except Exception as e:
            tb = traceback.format_exc()
            logging.error(f"Job {analysis_id} failed: {tb}")
            with open(meta_path, "w") as f:
                json.dump({"status": "failed","reason": str(e)}, f)
            return {"status": "failed", "reason": str(e)}
