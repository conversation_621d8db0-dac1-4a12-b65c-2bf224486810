trigger:
    branches:
      include:
        - master
        - develop
  
variables:
    - group: fin-analyzer-secrets
    - name: APP_NAME
      value: "fin-analyzer"
    - name: APP_PORT_DEV
      value: "8001"
    - name: APP_PORT_PROD
      value: "8000"
    - name: DOCKER_IMAGE
      value: "fin-analyzer"

pool: fin-analyzer

stages:
- stage: Build
  displayName: "Build Docker Image"
  jobs:
    - job: Build
      displayName: "Build and Tag Docker Image"
      steps:
        - script: |
            if [ "$BUILD_SOURCEBRANCHNAME" == "develop" ]; then
                DOCKER_TAG="dev-${BUILD_SOURCEVERSION:0:8}"
                APP_NAME="$APP_NAME-dev"
                APP_PORT="$APP_PORT_DEV"
            elif [ "$BUILD_SOURCEBRANCHNAME" == "master" ]; then
                DOCKER_TAG="prod-${BUILD_SOURCEVERSION:0:8}"
                APP_NAME="$APP_NAME-prod"
                APP_PORT="$APP_PORT_PROD"
            fi
            echo "DOCKER_TAG: $DOCKER_TAG"
            echo "APP_NAME: $APP_NAME"
            echo "APP_PORT: $APP_PORT"
            echo "##vso[task.setvariable variable=DOCKER_TAG_IN_TASK]$DOCKER_TAG"
            echo "##vso[task.setvariable variable=DOCKER_TAG;isOutput=true]$DOCKER_TAG"
            echo "##vso[task.setvariable variable=APP_NAME;isOutput=true]$APP_NAME"
            echo "##vso[task.setvariable variable=APP_PORT;isOutput=true]$APP_PORT"
          displayName: "Set Variables Based on Branch"
          name: setvars

        - script: |
            echo "Creating gdrive_readonly_key.json..."
            mkdir service_accounts
            cat > service_accounts/gdrive_readonly_key.json <<EOF
            {
              "type": "$(GDRIVE_TYPE)",
              "project_id": "$(GDRIVE_PROJECT_ID)",
              "private_key_id": "$(GDRIVE_PRIVATE_KEY_ID)",
              "private_key": "$(GDRIVE_PRIVATE_KEY)",
              "client_email": "**********************",
              "client_id": "$(GDRIVE_CLIENT_ID)",
              "auth_uri": "$(GDRIVE_AUTH_URI)",
              "token_uri": "$(GDRIVE_TOKEN_URI)",
              "auth_provider_x509_cert_url": "$(GDRIVE_AUTH_PROVIDER_CERT_URL)",
              "client_x509_cert_url": "$(GDRIVE_CLIENT_CERT_URL)",
              "universe_domain": "$(GDRIVE_UNIVERSE_DOMAIN)"
            }
            EOF
          displayName: "Create Google Drive Credentials File"
          env:
            GDRIVE_TYPE: $(GDRIVE_TYPE)
            GDRIVE_PROJECT_ID: $(GDRIVE_PROJECT_ID)
            GDRIVE_PRIVATE_KEY_ID: $(GDRIVE_PRIVATE_KEY_ID)
            GDRIVE_PRIVATE_KEY: $(GDRIVE_PRIVATE_KEY)
            GDRIVE_CLIENT_EMAIL: **********************
            GDRIVE_CLIENT_ID: $(GDRIVE_CLIENT_ID)
            GDRIVE_AUTH_URI: $(GDRIVE_AUTH_URI)
            GDRIVE_TOKEN_URI: $(GDRIVE_TOKEN_URI)
            GDRIVE_AUTH_PROVIDER_CERT_URL: $(GDRIVE_AUTH_PROVIDER_CERT_URL)
            GDRIVE_CLIENT_CERT_URL: $(GDRIVE_CLIENT_CERT_URL)
            GDRIVE_UNIVERSE_DOMAIN: $(GDRIVE_UNIVERSE_DOMAIN)

        - script: |
            echo "Building Docker image with tag: $(DOCKER_IMAGE):$(DOCKER_TAG_IN_TASK)"
            docker build \
              --build-arg LANGSMITH_TRACING="$(LANGSMITH_TRACING)" \
              --build-arg LANGSMITH_PROJECT="$(LANGSMITH_PROJECT)" \
              --build-arg LANGSMITH_ENDPOINT="$(LANGSMITH_ENDPOINT)" \
              --build-arg OCR_ENGINE="$(OCR_ENGINE)" \
              --build-arg OPENAI_API_KEY="$(OPENAI_API_KEY)" \
              --build-arg GROQ_API_KEY="$(GROQ_API_KEY)" \
              --build-arg GOOGLE_API_KEY="$(GOOGLE_API_KEY)" \
              --build-arg LANGSMITH_API_KEY="$(LANGSMITH_API_KEY)" \
              --build-arg ABBYY_APP_ID="$(ABBYY_APP_ID)" \
              --build-arg ABBYY_PASSWORD="$(ABBYY_PASSWORD)" \
              -t $(DOCKER_IMAGE):$(DOCKER_TAG_IN_TASK) .
          displayName: "Build Docker Image"
          env:
            LANGSMITH_TRACING: $(LANGSMITH_TRACING)
            LANGSMITH_PROJECT: $(LANGSMITH_PROJECT)
            LANGSMITH_ENDPOINT: $(LANGSMITH_ENDPOINT)
            OCR_ENGINE: $(OCR_ENGINE)
            OPENAI_API_KEY: $(OPENAI_API_KEY)
            GROQ_API_KEY: $(GROQ_API_KEY)
            GOOGLE_API_KEY: $(GOOGLE_API_KEY)
            LANGSMITH_API_KEY: $(LANGSMITH_API_KEY)
            ABBYY_APP_ID: $(ABBYY_APP_ID)
            ABBYY_PASSWORD: $(ABBYY_PASSWORD)

- stage: Deploy
  displayName: "Deploy Docker Container"
  dependsOn: Build
  jobs:
    - job: Deploy
      displayName: "Deploy Application"
      variables:
        - name: DOCKER_TAG
          value: $[ stageDependencies.Build.Build.outputs['setvars.DOCKER_TAG'] ]
        - name: APP_NAME
          value: $[ stageDependencies.Build.Build.outputs['setvars.APP_NAME'] ]
        - name: APP_PORT
          value: $[ stageDependencies.Build.Build.outputs['setvars.APP_PORT'] ]
      steps:
        - script: |
            if docker ps -a --filter name=$APP_NAME --format "{{.Names}}" | grep -q $APP_NAME; then
                docker rm -f $(docker ps -aq --filter name=$APP_NAME)
            fi
            docker run -d -p $APP_PORT:8000 --restart on-failure --name $APP_NAME $(DOCKER_IMAGE):$(DOCKER_TAG)
          displayName: "Run Docker Container"

- stage: Cleanup
  displayName: "Cleanup Working Directory"
  dependsOn: Deploy
  jobs:
    - job: Cleanup
      displayName: "Remove Working Directory"
      steps:
        - script: |
            echo "Cleaning up working directory..."
            rm -rf $(Build.SourcesDirectory)/*
          displayName: "Cleanup Working Directory"