import os
import json
from dotenv import load_dotenv
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain.chains import RetrievalQA
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from validation.schema import RatioAnalysis

# Load environment
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Set up embedding and retriever
embedding_model = OpenAIEmbeddings(
    model="text-embedding-3-small",
    openai_api_key=OPENAI_API_KEY
)
vectorstore = FAISS.load_local("outputs/faiss_index", embedding_model, allow_dangerous_deserialization=True)
retriever = vectorstore.as_retriever()
#retriever.search_kwargs['k'] = 8
# Set up parser
parser = JsonOutputParser(pydantic_object=RatioAnalysis)

# Prompt
prompt = PromptTemplate(
    template="""
You are a Financial Statement Analyzer .
                        Analyze the provided financial context step-by-step to extract financial insights.

                        Specifically:
                        - Focus on Ratio-based analysis ( current_ratio, quick_ratio, debt_to_equity, net_profit_margin, etc.)

{format_instructions}

Context:
{context}

Question:
{question}
""",
    input_variables=["context", "question"],
    partial_variables={"format_instructions": parser.get_format_instructions()}
)


# Set up model and chain
llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0)
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    retriever=retriever,
    return_source_documents=False,
    chain_type_kwargs={"prompt": prompt}
)

# Query




query = ("Based on the financial statements, provide ratio-based financial analysis.")


# Run and parse
result = qa_chain.invoke({"query": query})
parsed = parser.parse(result["result"])

# Output
print(json.dumps(parsed, indent=2))
