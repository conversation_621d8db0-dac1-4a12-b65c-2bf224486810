# run_with_pyinstrument.py
"""
Simple script to run the FastAPI app with PyInstrument profiling
"""
import uvicorn
import os

def main():
    """Run the FastAPI application"""
    print("🚀 Starting AI Financial Analyzer")
    print("🔍 PyInstrument profiling is enabled for all APIs")
    print("📊 HTML reports will be saved to: profiling_results/")
    print("🌐 API docs: http://localhost:8000/docs")
    print("-" * 50)
    
    # Create profiling directory
    os.makedirs("profiling_results", exist_ok=True)
    
    # Use environment variable for host, default to localhost for local development
    host = os.getenv("HOST", "127.0.0.1")
    
    try:
        uvicorn.run(
            "routers.routes:app", 
            host=host, 
            port=8000, 
            reload=True,  # Enable reload for development
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        print("📊 Check profiling_results/ for performance reports!")

if __name__ == "__main__":
    main()
