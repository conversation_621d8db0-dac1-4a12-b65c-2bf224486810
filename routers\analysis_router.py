from fastapi import APIRouter, status, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from dto.financial_request import FinancialRequest
from dto.market_request import MarketRequest
from dto.interim_financial_request import InterimFinancialRequest
from dto.entity_background_request import EntityBackgroundRequest
from service.analysis_service import handle_request
from service.analysis_service import get_analysis_result
from utils.file_utils import setup_logging

setup_logging()
router = APIRouter()

@router.post("/api/ai/analysis/financial_analysis")
def perform_analyse(req: FinancialRequest, background_tasks: BackgroundTasks):

    analysis_id = handle_request("financial_analysis", req, background_tasks)

    return JSONResponse(
        status_code=status.HTTP_201_CREATED,
        content={"analysis_id": analysis_id, "status": "queued"}
    )

@router.post("/api/ai/analysis/market")
def perform_analyse(req: MarketRequest, background_tasks: BackgroundTasks):

    analysis_id = handle_request("market_analysis", req, background_tasks)

    return JSONResponse(
        status_code=status.HTTP_201_CREATED,
        content={"analysis_id": analysis_id, "status": "queued"}
    )

@router.post("/api/ai/analysis/financials/interim")
def perform_interim_financial_analysis(req: InterimFinancialRequest, background_tasks: BackgroundTasks):

    analysis_id = handle_request("interm_financial_analysis", req, background_tasks)

    return JSONResponse(
        status_code=status.HTTP_201_CREATED,
        content={"analysis_id": analysis_id, "status": "queued"}
    )

@router.post("/api/ai/analysis/entity-background")
def perform_entity_background_analysis(req:EntityBackgroundRequest,background_tasks:BackgroundTasks):
    analysis_id=handle_request("entity_background_analysis",req,background_tasks)
    return JSONResponse(
        status_code=status.HTTP_201_CREATED,
        content={"analysis_id":analysis_id,"status":"queued"}
    )


@router.get("/api/ai/get_analysis_result")
def get_analysis(analysis_id: str = Query(...)):
    return get_analysis_result(analysis_id)