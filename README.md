#  AI Financial Statement Analyzer

This project is a production-ready AI-powered API system designed to analyze financial documents (PDFs or Excel files) using OCR and large language models (LLMs). It extracts key financial insights, particularly ratio-based and trend-based analyses.

##  Project Structure

```
AI-Fin-Analyzer/
│
├── run.py                     # Entry point to run the FastAPI server
├── .env                       # Contains API keys and configs
├── routers/                   # API route implementations
│   ├── __init__.py
│   ├── routes.py              # Registers all API routes
│   ├── validate_api.py        # API to validate uploaded documents
│   ├── analyse_api.py         # API to process and embed documents
│   └── results_api.py         # API to query financial insights
│
├── document_processor.py      # Loads, cleans, and chunks documents
├── ocr_engine.py             # Performs OCR using Tesseract or ABBYY
├── schema.py                  # Pydantic models for output schemas
├── requirements.txt           # Python dependencies
└── data_company_documents/     # Input company folders with .pdf/.xlsx files
```

---

##  Features

-  OCR support (Tesseract / ABBYY)
-  PDF and Excel document handling
-  Vector embeddings using OpenAI
-  FAISS-based semantic search
-  Ratio-based financial analysis (Current Ratio, Quick Ratio, etc.)
-  RESTful API endpoints with FastAPI

---

##  How to Run the Project After Cloning

### 1. Clone the repository

```bash
cd AI-Fin-Analyzer
```

### 2. Create virtual environment

```bash
conda create -n ai-fin-analyzer python=3.12
conda activate ai-fin-analyzer
```

### 3. Install dependencies

```bash
pip install -r requirements.txt
```

### 4. Set up `.env` file

Create a `.env` file in the root directory with:

```
OPENAI_API_KEY="write your OpenAI API Key here"
GROQ_API_KEY="write your GROQ API Key here"

LANGSMITH_TRACING=true
LANGSMITH_PROJECT="financial-analyzer"
LANGSMITH_API_KEY="write your LangSmith API Key here"
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"

ABBYY_APP_ID="write your ABBYY App ID here"
ABBYY_PASSWORD="write your ABBYY Password here"
OCR_ENGINE="tesseract"  # or "abbyy"
```

---

##  Running the Server

Make sure the `routers` folder contains a file `__init__.py` (even if empty).

Then run:

```bash
python run.py
```

Or using Uvicorn directly:

```bash
uvicorn routers.routes:app --reload
```

Visit the interactive API docs:

```
http://127.0.0.1:8000/docs
```

---

##  Example API Flow

1. **Validate documents**
   - `POST /api/ai/validate-documents`
   - Payload: `{ "dir_url": "data_company_documents/my_company" }`

2. **Analyze documents**
   - `POST /api/ai/analyse`
   - Same payload as above

3. **Get analysis results**
   - `GET /api/ai/get-results/my_company`

---

##  Notes

- Input documents must be placed under `data_company_documents/<company_id>/`
- Supported formats: `.pdf`, `.xlsx`
- Results are stored in `outputs/<company_id>/`

---



##  Folder Creation Behavior

| Folder                         | Auto-Created? | Notes                                             |
|-------------------------------|---------------|---------------------------------------------------|
| `data_company_documents/`     |  Yes        | Created automatically when the server runs.       |
| `data_company_documents/<id>` |  No         | Must be created manually with company files inside. |
| `outputs/`                    |  Yes        | Automatically created during document analysis.   |
| `outputs/<company_id>/`       |  Yes        | Stores FAISS index and chunked document data.     |

If you're running the project for the first time, make sure to manually create:

```
data_company_documents/<company_id>
```

And place `.pdf` or `.xlsx` financial files inside it before hitting the APIs.
---

