
# AI Financial Statement Analyzer

This is a production-grade RAG (Retrieval-Augmented Generation) application that uses OCR + LLMs to analyze financial documents (PDF/XLSX) and return structured ratio-based insights through a FastAPI interface.

---

##  Project Structure

```
AI-FIN-ANALYZER/
│
├── run.py                      # Entry point for FastAPI app
├── Dockerfile                  # Docker build file
├── requirements.txt            # Python dependencies
├── .env.example                # Example env file (rename to .env)
│
├── routers/                    # All API endpoints
│   ├── routes.py
│   ├── analyse_api.py
│   ├── results_api.py
│   └── validate_api.py
│
├── service_accounts_example/   # rename to service_accounts 
│   └── gdrive_readonly_key.json
│
├── utils/                      # Helper modules
│   ├── gdrive_utils.py
│   └── file_utils.py
│
├── validation/            # Pydantic schemas for output validation
├── outputs/        # Saved chunked data + FAISS index per company
├── data_company_documents/ # Your input PDF/XLSX files per company
├── document_processor.py       # Load, clean, chunk files
├── ocr_engine.py               # OCR logic (Tesseract or ABBYY)
└── README.md
```

---

##  Setup Instructions (Local)

### 1. <PERSON>lone and Navigate
```bash
git clone <repo-url>
cd AI-FIN-ANALYZER
```

### 2. Create virtual environment
```bash
conda create -n ai-fin-analyzer python=3.12
conda activate ai-fin-analyzer
```

### 3. Install dependencies
```bash
pip install -r requirements.txt
```

### 4. Configure `.env`
Duplicate `.env.example` and rename it to `.env`, then fill in:

```
OPENAI_API_KEY=...
GROQ_API_KEY=...
ABBYY_APP_ID=...
ABBYY_PASSWORD=...
OCR_ENGINE=tesseract
```

---

##  Docker Setup

###  1. Rename sensitive files

Rename:
- `service_accounts_example/` → `service_accounts/`
- `gdrive_readonly_key.json` → actual service key file
- `.env.example` → `.env`

```bash
mv service_accounts_example service_accounts
mv .env.example .env
```

###  2. Build the Docker image

```bash
docker build -t ai-fin-analyser .
```

###  3. Run the Docker container

```bash
docker run --name mvp-api -p 8000:8000 ai-fin-analyser
```

Then visit:
```
http://localhost:8000/docs
```

---

##  API Flow

1. `POST /api/ai/validate-documents`  
   Validates files in a GDrive folder or local dir

2. `POST /api/ai/analyse`  
   Processes, chunks, and embeds the data

3. `GET /api/ai/get-results/<company_id>`  
   Returns structured JSON financial analysis

---

##  Notes

- Input folder: `data_company_documents/<company_id>/` (must exist)
- Output folder: `outputs/<company_id>/` (auto-created)

---

##  .gitignore Recommendations

Make sure to add sensitive files to `.gitignore`:

```
.env
service_accounts/
outputs/
data_company_documents/
__pycache__/
```

---

##  That's it!

You're ready to run a fully containerized AI financial statement analyzer 🚀  
Need help connecting to Pinecone, Qdrant, or LangSmith? Just ask!
