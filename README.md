#  AI Financial Statement Analyzer

This project is a production-ready AI-powered API system designed to analyze financial documents (PDFs or Excel files) using OCR and large language models (LLMs). It extracts key financial insights, particularly ratio-based and trend-based analyses.

##  Project Structure

```
AI-Fin-Analyzer/
│
├── run.py                     # Entry point to run the FastAPI server
├── .env                       # Contains API keys and configs
├── routers/                   # API route implementations
│   ├── __init__.py
│   ├── routes.py              # Registers all API routes
│   ├── validate_api.py        # API to validate uploaded documents
│   ├── analyse_api.py         # API to process and embed documents
│   └── results_api.py         # API to query financial insights
│
├── document_processor.py      # Loads, cleans, and chunks documents
├── ocr_engine.py             # Performs OCR using Tesseract or ABBYY
├── schema.py                  # Pydantic models for output schemas
├── requirements.txt           # Python dependencies
└── data_company_documents/     # Input company folders with .pdf/.xlsx files
```

---

##  Features

-  OCR support (Tesseract / ABBYY)
-  PDF and Excel document handling
-  Vector embeddings using OpenAI
-  FAISS-based semantic search
-  Ratio-based financial analysis (Current Ratio, Quick Ratio, etc.)
-  RESTful API endpoints with FastAPI

---

##  🚀 Quick Start

### Option 1: Docker (Recommended)

#### 1. Clone the repository
```bash
git clone <repository-url>
cd AI-Fin-Analyzer
```

#### 2. Set up `.env` file
Create a `.env` file in the root directory with your API keys:

```env
OPENAI_API_KEY="your-openai-api-key-here"
GROQ_API_KEY="your-groq-api-key-here"
GOOGLE_API_KEY="your-google-api-key-here"

LANGSMITH_TRACING=true
LANGSMITH_PROJECT="financial-analyzer"
LANGSMITH_API_KEY="your-langsmith-api-key-here"
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"

ABBYY_APP_ID="your-abbyy-app-id-here"
ABBYY_PASSWORD="your-abbyy-password-here"
OCR_ENGINE="tesseract"  # Options: "tesseract" or "abbyy"
```

#### 3. Build Docker Image
```bash
docker build -t mvp-analyser .
```

#### 4. Run Docker Container
```bash
# Basic run
docker run -p 8000:8000 mvp-analyser

# Run with named container
docker run -p 8000:8000 --name mvp-analyser-app mvp-analyser

# Run in background (detached)
docker run -d -p 8000:8000 --name mvp-analyser-app mvp-analyser

# Run with volume mounting (to persist data)
docker run -p 8000:8000 -v $(pwd)/data:/app/data -v $(pwd)/outputs:/app/outputs mvp-analyser
```

#### 5. Access the Application
- **API**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

---

### Option 2: Local Development

#### 1. Clone the repository
```bash
git clone <repository-url>
cd AI-Fin-Analyzer
```

#### 2. Create virtual environment
```bash
conda create -n ai-fin-analyzer python=3.12
conda activate ai-fin-analyzer
```

#### 3. Install dependencies
```bash
pip install -r requirements.txt
```

#### 4. Set up `.env` file
Same as Docker option above.

#### 5. Run the server
```bash
python run.py
```

Or using Uvicorn directly:
```bash
uvicorn routers.routes:app --reload
```

Visit: http://127.0.0.1:8000/docs

---

## 🐳 Docker Commands Reference

### Building
```bash
# Build the image
docker build -t mvp-analyser .

# Build with no cache (force rebuild)
docker build --no-cache -t mvp-analyser .
```

### Running
```bash
# Basic run
docker run -p 8000:8000 mvp-analyser

# Run with custom environment variables
docker run -p 8000:8000 -e OCR_ENGINE=abbyy mvp-analyser

# Run with volume mounting
docker run -p 8000:8000 \
  -v $(pwd)/data_company_documents:/app/data_company_documents \
  -v $(pwd)/outputs:/app/outputs \
  mvp-analyser
```

### Management
```bash
# List running containers
docker ps

# Stop container
docker stop mvp-analyser-app

# Start stopped container
docker start mvp-analyser-app

# View logs
docker logs mvp-analyser-app

# Remove container
docker rm mvp-analyser-app

# Remove image
docker rmi mvp-analyser
```

---

## 🔧 Docker Features

- **OCR Support**: Includes Tesseract OCR with English and Arabic language packs
- **System Dependencies**: Pre-installed PDF processing and image libraries
- **Environment Variables**: Configurable host settings for development vs production
- **Volume Support**: Mount local directories for data persistence
- **Health Monitoring**: Built-in logging and container management

---

##  Example API Flow

1. **Validate documents**
   - `POST /api/ai/validate-documents`
   - Payload: `{ "dir_url": "data_company_documents/my_company" }`

2. **Analyze documents**
   - `POST /api/ai/analyse`
   - Same payload as above

3. **Get analysis results**
   - `GET /api/ai/get-results/my_company`

---

##  Notes

- Input documents must be placed under `data_company_documents/<company_id>/`
- Supported formats: `.pdf`, `.xlsx`
- Results are stored in `outputs/<company_id>/`

---



##  Folder Creation Behavior

| Folder                         | Auto-Created? | Notes                                             |
|-------------------------------|---------------|---------------------------------------------------|
| `data_company_documents/`     |  Yes        | Created automatically when the server runs.       |
| `data_company_documents/<id>` |  No         | Must be created manually with company files inside. |
| `outputs/`                    |  Yes        | Automatically created during document analysis.   |
| `outputs/<company_id>/`       |  Yes        | Stores FAISS index and chunked document data.     |

If you're running the project for the first time, make sure to manually create:

```
data_company_documents/<company_id>
```

And place `.pdf` or `.xlsx` financial files inside it before hitting the APIs.
---

