import os
import json
import traceback
import logging
import time
from dotenv import load_dotenv
from pydantic import BaseModel
from service.tavily_service import search_in_websites
from dto.market_request import MarketRequest
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from utils.file_utils import setup_logging
from service.analysis.analysis_interface import Analysis

setup_logging()
load_dotenv()

class MarketAnalysis(Analysis):
    def perform_analysis(self, req: BaseModel, analysis_id: str) -> None:
        output_dir = os.path.join("outputs", analysis_id)
        meta_path = os.path.join(output_dir, "job_meta.json")
        result_path = os.path.join(output_dir, "ratios_result.json")

        url_list = ["stats.gov.sa",
                    "misa.gov.sa", 
                    "spa.gov.sa", 
                    "english.alarabiya.net/business", 
                    "statista.com",
                    "data.worldbank.org",
                    "stats.oecd.org",
                    "mckinsey.com/featured-insights",
                    "bloomberg.com/markets"]
        params = req
        sector = params.sectorId + ": " + params.classificationId + " ("+' '.join(params.businessActivityList) +")"

        print(sector)

        if not sector:
            sector = "agriculture"
        
        start_time = time.time()
        try:
            os.makedirs(output_dir, exist_ok=True)
            with open(meta_path, "w") as f:
                json.dump({"status": "processing"}, f)
            prompt = ChatPromptTemplate.from_messages([
                ("system", f"""
                 Objective:
                    Generate a concise, one-paragraph market analysis report in {sector}, industry classification, and business activities.
                    The analysis should cover both local (Saudi Arabia) and global market perspectives.
                
                 Local Market Analysis:

                    Describe the size and recent growth trends of the industry in Saudi Arabia.
                    Identify key market drivers (e.g., consumer demand, regulation, technology).
                    Mention any relevant regulatory or economic context.
                    search from these websites {url_list[0:4]}

                Global Market Analysis:

                    Highlight global trends, innovation pace, and technological disruptions.
                    Compare global dynamics with local conditions if relevant.
                    search from these websites {url_list[4:9]}

                Competitive Landscape:

                    Indicate whether the market is fragmented or dominated by a few players.
                    Mention entry barriers and pricing flexibility if applicable.
                    Overall Risk Level:

                    Conclude whether the market environment is supportive, neutral, or challenging for the company.
                    Output Style:

                    One short, connected paragraph.
                    No bullet points.
                    Use professional, analytical tone.
                    """),
                MessagesPlaceholder(variable_name="agent_scratchpad")])
            parsed_result = search_in_websites(prompt)

            with open(result_path, "w") as f:
                json.dump(parsed_result, f, indent=2)

            with open(meta_path, "w") as f:
                json.dump({"status": "done"}, f)

            duration = round(time.time() - start_time, 2)
            logging.info(f"Analysis job {analysis_id} completed in {duration} seconds")

        except Exception as e:
            tb = traceback.format_exc()
            logging.error(f"Job {analysis_id} failed: {tb}")
            with open(meta_path, "w") as f:
                json.dump({
                    "status": "failed",
                    "reason": str(e),
                }, f)