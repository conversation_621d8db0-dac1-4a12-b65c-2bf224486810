import logging
from logging.handlers import RotatingFileHandler
import base64
import os
import mimetypes
from utils.gdrive_utils import download_gdrive_folder_to_data_dir
from service.webclient import call_api

# def setup_logging(log_file="processing.log"):
#     logging.basicConfig(
#         filename=log_file,
#         level=logging.INFO,
#         format="%(asctime)s - %(levelname)s - %(message)s",
#         datefmt="%Y-%m-%d %H:%M:%S"
#     )


attachment_reference_id_param = "attachmentReferenceId"

def setup_logging(log_file="processing.log"):
    handler= RotatingFileHandler(
        log_file, maxBytes=10*1024*1024, backupCount=5
    )
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[
            handler,
            logging.StreamHandler()  # <-- prints to console
        ]
    )

def convert_to_bytes(base64File:str) -> bytes:
    try:
        # Decode the Base64 string
        decoded_data = base64.b64decode(base64File)
        return decoded_data
    except Exception as e:
        print(f"An error occurred: {e}")

def get_extension_from_base64(base64_string):
    if base64_string.startswith("data:"):
        # Extract the MIME type from the data URI
        parts = base64_string.split(';', 1)
        if len(parts) > 0:
            mime_type_part = parts[0].split(':', 1)
            if len(mime_type_part) > 1:
                mime_type = mime_type_part[1]
                # Guess the extension from the MIME type
                extension = mimetypes.guess_extension(mime_type)
                return extension
    return None

def retreive_file_from_base64(ref_id:str):
    # call their endpoint to retreive the base64 file
    base64_file = call_api(attachment_reference_id_param , ref_id)
    # pares the returned file
    converted_file = convert_to_bytes(base64_file)
    filename = ref_id + "." + get_extension_from_base64(base64_file)
    try:
        with open(filename, 'wb') as file:
            file.write(converted_file)
        print(f"Bytes successfully written to {filename}")
    except IOError as e:
        print(f"Error writing to file: {e}")
    print(type(file))
    print(file.name)
    # append the result to files list 
    return file

def retreive_files_from_google_drive(data_dir: str):
    files_list = []
    for filename in os.listdir(data_dir):
        files_list.append(filename)
    return files_list

def get_data_dir(dir_url: str):
    return download_gdrive_folder_to_data_dir(
            dir_url,
            base_dir="data_company_documents",
            service_account_file="service_accounts/gdrive_readonly_key.json"
        )
def load_document(data_dir: str, filename: str):
    file_path = os.path.join(data_dir, filename)
    with open(file_path, 'r') as file:
        return file