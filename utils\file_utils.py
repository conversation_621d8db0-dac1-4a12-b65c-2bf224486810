import logging
from logging.handlers import RotatingFileHandler

# def setup_logging(log_file="processing.log"):
#     logging.basicConfig(
#         filename=log_file,
#         level=logging.INFO,
#         format="%(asctime)s - %(levelname)s - %(message)s",
#         datefmt="%Y-%m-%d %H:%M:%S"
#     )



def setup_logging(log_file="processing.log"):
    handler= RotatingFileHandler(
        log_file, maxBytes=10*1024*1024, backupCount=5
    )
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[
            handler,
            logging.StreamHandler()  # <-- prints to console
        ]
    )
