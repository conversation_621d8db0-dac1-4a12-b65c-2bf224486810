# analyze_profiling_results.py
"""
<PERSON>ript to analyze VizTracer profiling results and generate performance reports
"""
import json
import os
import glob
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Any

class ProfilingAnalyzer:
    def __init__(self, profiling_dir="profiling_results"):
        self.profiling_dir = profiling_dir
        self.metrics_data = []
        self.trace_files = []
    
    def load_metrics_files(self):
        """Load all metrics JSON files"""
        pattern = os.path.join(self.profiling_dir, "*_metrics_*.json")
        metrics_files = glob.glob(pattern)
        
        for file_path in metrics_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    data['file_source'] = os.path.basename(file_path)
                    self.metrics_data.append(data)
            except Exception as e:
                print(f"Error loading {file_path}: {e}")
        
        print(f"📊 Loaded {len(self.metrics_data)} metrics files")
    
    def load_trace_files(self):
        """Find all VizTracer trace files"""
        pattern = os.path.join(self.profiling_dir, "*.json")
        all_files = glob.glob(pattern)
        
        # Filter out metrics files
        self.trace_files = [f for f in all_files if "_metrics_" not in f]
        print(f"🔍 Found {len(self.trace_files)} trace files")
    
    def analyze_api_performance(self):
        """Analyze API endpoint performance"""
        api_metrics = [m for m in self.metrics_data if m['operation'].startswith('api_')]
        
        if not api_metrics:
            print("❌ No API metrics found")
            return
        
        print("\n" + "="*60)
        print("📊 API PERFORMANCE ANALYSIS")
        print("="*60)
        
        # Sort by duration
        api_metrics.sort(key=lambda x: x['duration_seconds'], reverse=True)
        
        print(f"{'Endpoint':<30} {'Duration (s)':<12} {'Memory (MB)':<12} {'CPU %':<8}")
        print("-" * 62)
        
        for metric in api_metrics:
            endpoint = metric['operation'].replace('api_', '')
            print(f"{endpoint:<30} {metric['duration_seconds']:<12.3f} "
                  f"{metric['memory_delta_mb']:<12.2f} {metric['cpu_percent']:<8.1f}")
        
        # Calculate averages
        avg_duration = sum(m['duration_seconds'] for m in api_metrics) / len(api_metrics)
        avg_memory = sum(m['memory_delta_mb'] for m in api_metrics) / len(api_metrics)
        avg_cpu = sum(m['cpu_percent'] for m in api_metrics) / len(api_metrics)
        
        print("-" * 62)
        print(f"{'AVERAGE':<30} {avg_duration:<12.3f} {avg_memory:<12.2f} {avg_cpu:<8.1f}")
    
    def analyze_ocr_performance(self):
        """Analyze OCR operation performance"""
        ocr_metrics = [m for m in self.metrics_data if m['operation'].startswith('ocr_')]
        
        if not ocr_metrics:
            print("❌ No OCR metrics found")
            return
        
        print("\n" + "="*60)
        print("🔍 OCR PERFORMANCE ANALYSIS")
        print("="*60)
        
        # Group by OCR engine
        tesseract_metrics = [m for m in ocr_metrics if 'tesseract' in m['operation']]
        abbyy_metrics = [m for m in ocr_metrics if 'abbyy' in m['operation']]
        
        if tesseract_metrics:
            avg_tesseract_time = sum(m['duration_seconds'] for m in tesseract_metrics) / len(tesseract_metrics)
            avg_tesseract_memory = sum(m['memory_delta_mb'] for m in tesseract_metrics) / len(tesseract_metrics)
            print(f"Tesseract OCR - Avg Time: {avg_tesseract_time:.3f}s, Avg Memory: {avg_tesseract_memory:.2f}MB")
        
        if abbyy_metrics:
            avg_abbyy_time = sum(m['duration_seconds'] for m in abbyy_metrics) / len(abbyy_metrics)
            avg_abbyy_memory = sum(m['memory_delta_mb'] for m in abbyy_metrics) / len(abbyy_metrics)
            print(f"ABBYY OCR - Avg Time: {avg_abbyy_time:.3f}s, Avg Memory: {avg_abbyy_memory:.2f}MB")
        
        # Page-level analysis
        page_metrics = [m for m in ocr_metrics if 'page_' in m['operation']]
        if page_metrics:
            print(f"\nPage Processing:")
            print(f"  Total Pages: {len(page_metrics)}")
            print(f"  Avg Time per Page: {sum(m['duration_seconds'] for m in page_metrics) / len(page_metrics):.3f}s")
            print(f"  Avg Memory per Page: {sum(m['memory_delta_mb'] for m in page_metrics) / len(page_metrics):.2f}MB")
    
    def analyze_rag_performance(self):
        """Analyze RAG operation performance"""
        rag_metrics = [m for m in self.metrics_data if m['operation'].startswith('rag_')]
        
        if not rag_metrics:
            print("❌ No RAG metrics found")
            return
        
        print("\n" + "="*60)
        print("🧠 RAG PERFORMANCE ANALYSIS")
        print("="*60)
        
        for metric in rag_metrics:
            operation = metric['operation'].replace('rag_', '')
            print(f"{operation}: {metric['duration_seconds']:.3f}s, {metric['memory_delta_mb']:.2f}MB")
    
    def analyze_memory_usage(self):
        """Analyze memory usage patterns"""
        if not self.metrics_data:
            return
        
        print("\n" + "="*60)
        print("💾 MEMORY USAGE ANALYSIS")
        print("="*60)
        
        # Find operations with highest memory usage
        high_memory_ops = sorted(self.metrics_data, key=lambda x: x['memory_delta_mb'], reverse=True)[:5]
        
        print("Top 5 Memory Consuming Operations:")
        for i, op in enumerate(high_memory_ops, 1):
            print(f"{i}. {op['operation']}: {op['memory_delta_mb']:.2f}MB")
        
        # Memory usage distribution
        memory_deltas = [m['memory_delta_mb'] for m in self.metrics_data]
        print(f"\nMemory Usage Statistics:")
        print(f"  Total Memory Delta: {sum(memory_deltas):.2f}MB")
        print(f"  Average Memory Delta: {sum(memory_deltas)/len(memory_deltas):.2f}MB")
        print(f"  Max Memory Delta: {max(memory_deltas):.2f}MB")
        print(f"  Min Memory Delta: {min(memory_deltas):.2f}MB")
    
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.profiling_dir, f"performance_report_{timestamp}.txt")
        
        with open(report_file, 'w') as f:
            f.write("AI FINANCIAL ANALYZER - PERFORMANCE REPORT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Operations Analyzed: {len(self.metrics_data)}\n")
            f.write(f"Trace Files Found: {len(self.trace_files)}\n\n")
            
            # Summary statistics
            if self.metrics_data:
                total_time = sum(m['duration_seconds'] for m in self.metrics_data)
                total_memory = sum(m['memory_delta_mb'] for m in self.metrics_data)
                
                f.write("SUMMARY STATISTICS\n")
                f.write("-" * 20 + "\n")
                f.write(f"Total Execution Time: {total_time:.3f} seconds\n")
                f.write(f"Total Memory Usage: {total_memory:.2f} MB\n")
                f.write(f"Average Operation Time: {total_time/len(self.metrics_data):.3f} seconds\n")
                f.write(f"Average Memory per Operation: {total_memory/len(self.metrics_data):.2f} MB\n\n")
                
                # Detailed breakdown
                f.write("DETAILED BREAKDOWN\n")
                f.write("-" * 20 + "\n")
                for metric in sorted(self.metrics_data, key=lambda x: x['duration_seconds'], reverse=True):
                    f.write(f"{metric['operation']}: {metric['duration_seconds']:.3f}s, "
                           f"{metric['memory_delta_mb']:.2f}MB\n")
        
        print(f"📄 Performance report saved to: {report_file}")
    
    def run_analysis(self):
        """Run complete profiling analysis"""
        print("🔍 Starting profiling analysis...")
        
        self.load_metrics_files()
        self.load_trace_files()
        
        if not self.metrics_data:
            print("❌ No profiling data found. Run the application with profiling first.")
            return
        
        self.analyze_api_performance()
        self.analyze_ocr_performance()
        self.analyze_rag_performance()
        self.analyze_memory_usage()
        self.generate_performance_report()
        
        print(f"\n✅ Analysis complete! Check {self.profiling_dir}/ for detailed results.")
        print(f"🌐 To view VizTracer traces, use: vizviewer {self.trace_files[0] if self.trace_files else 'trace_file.json'}")

def main():
    """Main function"""
    analyzer = ProfilingAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
