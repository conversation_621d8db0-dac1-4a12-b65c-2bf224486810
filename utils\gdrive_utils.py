# utils/gdrive_utils.py
import os
import io
import re
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload

class GoogleDriveStrategy:
    """Google Drive storage strategy"""
    def __init__(self, service_account_file: str, scopes: list):
        self.credentials = service_account.Credentials.from_service_account_file(
            service_account_file, scopes=scopes
        )
        self.service = build('drive', 'v3', credentials=self.credentials)

    def list_supported_files(self, folder_id: str) -> list:
        """List all PDF and XLSX files in the given Google Drive folder"""
        query = (
            f"'{folder_id}' in parents and "
            "(mimeType='application/pdf' or mimeType='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')"
        )
        results = self.service.files().list(q=query, fields="files(id, name)").execute()
        return results.get("files", [])


    def download_file(self, file_id: str) -> io.BytesIO:
        request = self.service.files().get_media(fileId=file_id)
        fh = io.BytesIO()
        downloader = MediaIoBaseDownload(fh, request)
        done = False
        while not done:
            __, done = downloader.next_chunk()
        fh.seek(0)
        return fh

    def clean_url(self, url: str) -> str:
        """Extract folder ID from Google Drive URL using regex"""
        pattern = r'(?:folders/|id=)([a-zA-Z0-9_-]+)'
        match = re.search(pattern, url)
        if match:
            return match.group(1)
        raise ValueError("Invalid URL format: No folder ID found")

def download_gdrive_folder_to_data_dir(
    gdrive_url: str,
    base_dir: str = "data_company_documents",
    service_account_file: str = "service_accounts/gdrive_readonly_key.json",
    max_files: int = 30
) -> str:
    """
    Downloads PDF and XLSX files from GDrive folder to: data_company_documents/<folder_id>
    """
    SCOPES = ['https://www.googleapis.com/auth/drive.readonly']
    strategy = GoogleDriveStrategy(service_account_file, SCOPES)
    folder_id = strategy.clean_url(gdrive_url)

    target_dir = os.path.join(base_dir, folder_id)
    os.makedirs(target_dir, exist_ok=True)

    files = strategy.list_supported_files(folder_id)[:max_files]
    for file_meta in files:
        file_id = file_meta['id']
        file_name = file_meta['name']
        stream = strategy.download_file(file_id)
        with open(os.path.join(target_dir, file_name), "wb") as f:
            f.write(stream.read())

    return target_dir

