# results_api.py

from fastapi import APIRouter, HTTPException,status
from fastapi.responses import JSONResponse
import os
from dotenv import load_dotenv
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain.chains import RetrievalQA
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from validation.schema import RatioAnalysis
from utils.profiling_utils import profile_api_endpoint, MemoryTracker
import time
import logging
from utils.file_utils import setup_logging


setup_logging()
load_dotenv()
router = APIRouter()

@router.get("/api/ai/get-results/{company_id}")
@profile_api_endpoint("get_results")
def get_results(company_id: str):
    start_time = time.time()
    try:
        index_path = os.path.join("outputs", company_id, "faiss_index")
        if not os.path.exists(index_path):
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="FAISS index not found for this company.")

        embedding_model = OpenAIEmbeddings(
            model="text-embedding-3-small",
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )
        vectorstore = FAISS.load_local(index_path, embedding_model, allow_dangerous_deserialization=True)

        retriever = vectorstore.as_retriever()
        retriever.search_kwargs['k'] = 40

        parser = JsonOutputParser(pydantic_object=RatioAnalysis)
        prompt = PromptTemplate(
    template="""
You are a Financial Statement Analyzer.

Your task is to analyze provided financial data for multiple years. For each year available:

1. Clearly extract necessary financial figures.
2. Calculate and interpret each financial ratio:
    - Current Ratio = Current Assets / Current Liabilities
    - Quick Ratio = (Current Assets - Inventory) / Current Liabilities
    - Debt to Equity Ratio = Total Debt / Total Equity
    - Net Profit Margin = Net Profit / Revenue

For **each year**, provide:
- The calculated value.
- The calculation formula explicitly with values.
- A concise interpretation comment about the financial condition indicated by the ratio for that year.

Provide your response strictly in the following JSON format:

{format_instructions}

Context:
{context}

Question:
{question}
""",
    input_variables=["context", "question"],
    partial_variables={"format_instructions": parser.get_format_instructions()}
)



        llm = ChatOpenAI(model="gpt-4.1-mini", temperature=0)
        qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            retriever=retriever,
            return_source_documents=False,
            chain_type_kwargs={"prompt": prompt}
        )

       
        query = (
    "Based on the provided multi-year financial data, extract all necessary values to calculate and interpret the following financial ratios for each available year:\n"
    "- Current Ratio = Current Assets / Current Liabilities\n"
    "- Quick Ratio = (Current Assets - Inventory) / Current Liabilities\n"
    "- Debt to Equity Ratio = Total Debt / Total Equity\n"
    "- Net Profit Margin = Net Profit / Revenue\n\n"
    "Ensure you retrieve and use the following fields explicitly where available: "
    "Current Assets, Current Liabilities, Inventory, Total Debt, Total Equity, Net Profit, and Revenue.\n\n"
    "Present the results year by year. For each ratio and year, include:\n"
    "1. The substituted formula with real numbers\n"
    "2. The computed value (rounded to two decimal places)\n"
    "3. A short interpretation of what the ratio indicates about financial health."
)




        
        raw_result = qa_chain.invoke({"query": query})
        parsed_result = parser.parse(raw_result["result"])
        end_time = time.time()
        duration = round(end_time - start_time, 2)
        logging.info(f"Time taken: {duration} seconds")


        return JSONResponse(status_code=status.HTTP_200_OK,content=parsed_result)
    
    
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"status": "error", "reason": str(e)}
        )
