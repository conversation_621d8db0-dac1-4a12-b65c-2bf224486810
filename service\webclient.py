import requests
import os

def call_api(endpoint_url: str, param_key: str, param_value: str):
    url = os.getenv("LENDGATE_BASE_URL")
    try:
        response = requests.get(url + endpoint_url, params={param_key: param_value})
        response.raise_for_status()  # Raises an error for bad HTTP status codes
        return response.text 
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None