from fastapi import status, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uuid
import os
import json
import traceback
import logging
from service.analysis.analysis_interface import Analysis
from service.analysis.financial_analysis import FinancialAnalysis
from service.analysis.interm_finaincals_analysis import IntermFinancialAnalysis
from service.analysis.market_analysis import MarketAnalysis
from service.analysis.entity_background_analysis import EntityBackgroundAnalysis
from utils.file_utils import setup_logging

attachment_reference_id_param = "attachmentReferenceId"

setup_logging()

analysis_factory = {
    "interm_financial_analysis": IntermFinancialAnalysis,
    "financial_analysis": FinancialAnalysis,
    "market_analysis": MarketAnalysis,
    "entity_background_analysis":EntityBackgroundAnalysis

}

def handle_request(type:str, req: BaseModel, background_tasks: BackgroundTasks):
    # generate UUID analysis id
    analysis_id = str(uuid.uuid4())
    
    # schdule analysis 
    background_tasks.add_task(run_analysis_job, type, req, analysis_id)

    #return generated analysis_id
    return analysis_id


def run_analysis_job(type:str, req: BaseModel, analysis_id:str):
    # run desired analysis based on the provided type
    analysis_class = analysis_factory.get(type.lower())
    analyzer: Analysis = analysis_class()

    analyzer.perform_analysis(req, analysis_id)


def get_analysis_result(analysis_id: str):
    output_dir = os.path.join("outputs", analysis_id)
    meta_path = os.path.join(output_dir, "job_meta.json")
    result_path = os.path.join(output_dir, "ratios_result.json")
    try:
        if not os.path.exists(meta_path):
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "status": "not_found",
                    "message": (
                        "analysis_id not found: The specified analysis_id does not exist. "
                        "This may be because the analysis_id was never created, was deleted, "
                        "or the ID is incorrect. Please check your analysis_id ID and try again."
                    )
                }
            )

        with open(meta_path, "r") as f:
            meta = json.load(f)

        if meta["status"] == "done" and os.path.exists(result_path):
            with open(result_path, "r") as f:
                result = json.load(f)
            return JSONResponse(status_code=status.HTTP_200_OK, content={"status": "done", "result": result})

        elif meta["status"] == "failed":
            return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=meta)

        else:
            return JSONResponse(status_code=status.HTTP_200_OK, content=meta)
        
    except Exception as e:
        tb = traceback.format_exc()
        logging.error(f"analysis_id {analysis_id} failed in result API: {tb}")
        with open(meta_path, "w") as f:
            json.dump({"status": "failed", "reason": str(e)}, f)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"status": "failed", "reason": str(e)}
        )
