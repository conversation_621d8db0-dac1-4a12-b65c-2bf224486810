# test_pyinstrument.py
"""
Simple script to test PyInstrument profiling with your AI Financial Analyzer
"""
import requests
import time
import json
import os
from datetime import datetime

class SimpleAPITester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.results = []
    
    def test_api(self, method, endpoint, data=None, description=""):
        """Test a single API endpoint"""
        url = f"{self.base_url}{endpoint}"
        
        print(f"🧪 Testing: {description}")
        start_time = time.time()
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, timeout=300)  # 5 min timeout
            elif method.upper() == "POST":
                response = requests.post(url, json=data, timeout=300)
            
            duration = time.time() - start_time
            
            result = {
                "endpoint": endpoint,
                "method": method,
                "description": description,
                "status_code": response.status_code,
                "duration_seconds": round(duration, 3),
                "success": response.status_code < 400,
                "timestamp": datetime.now().isoformat()
            }
            
            self.results.append(result)
            
            status = "✅" if result['success'] else "❌"
            print(f"{status} {description} - {response.status_code} - {duration:.3f}s")
            
            return response
            
        except Exception as e:
            print(f"❌ {description} - Error: {str(e)}")
            return None
    
    def run_basic_tests(self):
        """Run basic API tests"""
        print("🚀 Starting PyInstrument API Performance Tests...")
        print("📊 HTML reports will be saved to: profiling_results/")
        
        # Test data - you can modify this path
        test_data = {
            "dir_url": "data_company_documents/test_company"  # Change this to your test data
        }
        
        print(f"\n📁 Using test data: {test_data['dir_url']}")
        
        # 1. Test Validate API
        self.test_api(
            "POST", 
            "/api/ai/validate-documents", 
            test_data,
            "Document Validation"
        )
        
        # 2. Test Analyse API (this will trigger OCR and RAG profiling)
        self.test_api(
            "POST", 
            "/api/ai/analyse", 
            test_data,
            "Document Analysis (OCR + RAG)"
        )
        
        # 3. Test Results API
        company_id = os.path.basename(test_data["dir_url"])
        self.test_api(
            "GET", 
            f"/api/ai/get-results/{company_id}", 
            None,
            "Financial Analysis Results"
        )
        
        # Save results
        self.save_results()
        self.print_summary()
    
    def save_results(self):
        """Save test results"""
        os.makedirs("profiling_results", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"profiling_results/api_test_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Test results saved to: {results_file}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*60)
        print("📊 PYINSTRUMENT API TEST SUMMARY")
        print("="*60)
        
        if not self.results:
            print("❌ No test results")
            return
        
        total_time = sum(r['duration_seconds'] for r in self.results)
        successful_tests = [r for r in self.results if r['success']]
        
        print(f"Total Tests: {len(self.results)}")
        print(f"Successful: {len(successful_tests)}")
        print(f"Failed: {len(self.results) - len(successful_tests)}")
        print(f"Total Time: {total_time:.3f} seconds")
        
        if self.results:
            print(f"Average Time: {total_time/len(self.results):.3f} seconds")
        
        print("\n📋 Individual Results:")
        print("-" * 60)
        
        for result in self.results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['description']:<35} {result['duration_seconds']:>8.3f}s")
        
        print("\n🔍 Check profiling_results/ for detailed HTML reports!")
        print("💡 Open the HTML files in your browser to see call stacks and timing details.")

def check_server():
    """Check if the server is running"""
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """Main function"""
    print("🔍 PyInstrument API Performance Tester")
    print("=" * 50)
    
    # Check if server is running
    if not check_server():
        print("❌ Server is not running!")
        print("\n💡 Start the server first:")
        print("   python run.py")
        print("   or")
        print("   python run_profiled.py")
        return
    
    print("✅ Server is running")
    
    # Create tester and run tests
    tester = SimpleAPITester()
    tester.run_basic_tests()

if __name__ == "__main__":
    main()
