# utils/pyinstrument_profiler.py
"""
Simple PyInstrument profiling utilities for AI Financial Analyzer
"""
import os
import time
import functools
from datetime import datetime
from pyinstrument import Profiler
import logging

# Setup profiling directory
PROFILING_DIR = "profiling_results"
os.makedirs(PROFILING_DIR, exist_ok=True)

class SimpleProfiler:
    """Simple profiler wrapper for timing and memory tracking"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.profiler = None
    
    def start(self):
        """Start profiling"""
        self.start_time = time.time()
        self.profiler = Profiler()
        self.profiler.start()
        logging.info(f"🔍 Started profiling: {self.operation_name}")
    
    def stop(self):
        """Stop profiling and save results"""
        if not self.profiler:
            return
        
        self.profiler.stop()
        duration = time.time() - self.start_time if self.start_time else 0
        
        # Save HTML report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_file = os.path.join(PROFILING_DIR, f"{self.operation_name}_{timestamp}.html")
        
        with open(html_file, 'w') as f:
            f.write(self.profiler.output_html())
        
        logging.info(f"✅ Profiling complete: {self.operation_name} - {duration:.3f}s - Report: {html_file}")
        return {
            "operation": self.operation_name,
            "duration_seconds": round(duration, 3),
            "html_report": html_file,
            "timestamp": datetime.now().isoformat()
        }

def profile_function(operation_name: str):
    """Decorator to profile functions with minimal code changes"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            profiler = SimpleProfiler(operation_name)
            profiler.start()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                profiler.stop()
        
        return wrapper
    return decorator

def profile_api(api_name: str):
    """Decorator for API endpoints"""
    return profile_function(f"api_{api_name}")

def profile_ocr(engine_name: str):
    """Decorator for OCR operations"""
    return profile_function(f"ocr_{engine_name}")

def profile_rag(operation_name: str):
    """Decorator for RAG operations"""
    return profile_function(f"rag_{operation_name}")

class ProfileContext:
    """Context manager for profiling code blocks"""
    
    def __init__(self, operation_name: str):
        self.profiler = SimpleProfiler(operation_name)
    
    def __enter__(self):
        self.profiler.start()
        return self.profiler
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.profiler.stop()
        return False

# Quick profiling functions
def quick_profile(operation_name: str):
    """Quick context manager for profiling"""
    return ProfileContext(operation_name)

def time_function(func_name: str = None):
    """Simple timing decorator"""
    def decorator(func):
        name = func_name or func.__name__
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logging.info(f"⏱️ {name}: {duration:.3f}s")
            return result
        
        return wrapper
    return decorator
