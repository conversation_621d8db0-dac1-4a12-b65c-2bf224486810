from langchain.agents import create_openai_tools_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_tavily import TavilySearch
import getpass
import os

def search_in_websites(prompt):

    model = init_chat_model("gpt-4.1-mini", model_provider="openai")

    # Initialize Tavily Search Tool
    tavily_search_tool = TavilySearch(
        max_results=2,
        include_raw_content=True,
        include_answer=True,
        search_depth="advanced",
        topic="general",
    )

    tools = [tavily_search_tool]

    prompt = prompt
    # Create an agent that can use tools
    agent = create_openai_tools_agent(
        llm=model,
        tools=tools,
        prompt=prompt
    )

    # Create an Agent Executor to handle tool execution
    agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)
    #agent_executor = create_react_agent(model, tools)

    # Construct input properly as a dictionary
    response = agent_executor.invoke({"messages": []})
    content = response["output"]
    return content